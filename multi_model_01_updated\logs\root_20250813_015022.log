2025-08-13 01:50:22 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-13 01:50:23 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-13 01:50:23 - __main__ - INFO - ============================================================
2025-08-13 01:50:23 - __main__ - INFO - 测试AdaBoost修复效果
2025-08-13 01:50:23 - __main__ - INFO - ============================================================
2025-08-13 01:50:23 - __main__ - INFO - 训练集大小: (350, 10)
2025-08-13 01:50:23 - __main__ - INFO - 测试集大小: (150, 10)
2025-08-13 01:50:23 - __main__ - INFO - 开始测试包含AdaBoost的集成学习...
2025-08-13 01:50:23 - model_ensemble - INFO - ============================================================
2025-08-13 01:50:23 - model_ensemble - INFO - 开始运行集成学习管道
2025-08-13 01:50:23 - model_ensemble - INFO - ============================================================
2025-08-13 01:50:23 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-08-13 01:50:23 - model_ensemble - INFO - 集成方法: ['voting', 'boosting']
2025-08-13 01:50:23 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-08-13 01:50:23 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-08-13 01:50:23 - model_training - INFO - 模型名称: Random Forest
2025-08-13 01:50:23 - model_training - INFO - 准确率: 0.9800
2025-08-13 01:50:23 - model_training - INFO - AUC: 0.9996
2025-08-13 01:50:23 - model_training - INFO - AUPRC: 0.9996
2025-08-13 01:50:23 - model_training - INFO - 混淆矩阵:
2025-08-13 01:50:23 - model_training - INFO - 
[[74  1]
 [ 2 73]]
2025-08-13 01:50:23 - model_training - INFO - 
分类报告:
2025-08-13 01:50:23 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.97      0.99      0.98        75
           1       0.99      0.97      0.98        75

    accuracy                           0.98       150
   macro avg       0.98      0.98      0.98       150
weighted avg       0.98      0.98      0.98       150

2025-08-13 01:50:23 - model_training - INFO - 训练时间: 0.14 秒
2025-08-13 01:50:23 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9800
2025-08-13 01:50:23 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-13 01:50:23 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-13 01:50:23 - model_ensemble - INFO -   RandomForest 训练完成
2025-08-13 01:50:23 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-08-13 01:50:23 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.000 以处理不平衡
2025-08-13 01:50:23 - model_training - INFO - 模型名称: XGBoost
2025-08-13 01:50:23 - model_training - INFO - 准确率: 0.9933
2025-08-13 01:50:23 - model_training - INFO - AUC: 0.9996
2025-08-13 01:50:23 - model_training - INFO - AUPRC: 0.9997
2025-08-13 01:50:23 - model_training - INFO - 混淆矩阵:
2025-08-13 01:50:23 - model_training - INFO - 
[[75  0]
 [ 1 74]]
2025-08-13 01:50:23 - model_training - INFO - 
分类报告:
2025-08-13 01:50:23 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.99      1.00      0.99        75
           1       1.00      0.99      0.99        75

    accuracy                           0.99       150
   macro avg       0.99      0.99      0.99       150
weighted avg       0.99      0.99      0.99       150

2025-08-13 01:50:23 - model_training - INFO - 训练时间: 0.13 秒
2025-08-13 01:50:23 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9933
2025-08-13 01:50:23 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-13 01:50:23 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-13 01:50:23 - model_ensemble - INFO -   XGBoost 训练完成
2025-08-13 01:50:23 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-08-13 01:50:23 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-08-13 01:50:23 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-08-13 01:50:23 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 01:50:23 - model_ensemble - INFO -     voting_soft - 准确率: 0.9933, F1: 0.9933
2025-08-13 01:50:23 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-08-13 01:50:23 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 01:50:23 - model_ensemble - INFO -     voting_hard - 准确率: 0.9867, F1: 0.9867
2025-08-13 01:50:23 - model_ensemble - INFO - 步骤2: 运行集成方法 - boosting
2025-08-13 01:50:23 - model_ensemble - INFO - 开始训练集成模型，方法: boosting
2025-08-13 01:50:23 - model_ensemble - ERROR - 集成方法 boosting 失败: AdaBoostClassifier.__init__() got an unexpected keyword argument 'base_estimator'
2025-08-13 01:50:23 - model_ensemble - INFO - ============================================================
2025-08-13 01:50:23 - model_ensemble - INFO - 集成学习结果总结
2025-08-13 01:50:23 - model_ensemble - INFO - ============================================================
2025-08-13 01:50:23 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-08-13 01:50:23 - model_ensemble - INFO - 最佳F1分数: 0.9933
2025-08-13 01:50:23 - model_ensemble - INFO - 最佳准确率: 0.9933
2025-08-13 01:50:23 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-08-13 01:50:23 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9933, 精确率: 0.9934, 召回率: 0.9933, F1: 0.9933, AUC: 1.0000
2025-08-13 01:50:23 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9867, 精确率: 0.9870, 召回率: 0.9867, F1: 0.9867, AUC: 0.0000
2025-08-13 01:50:23 - __main__ - INFO - ✅ AdaBoost集成测试成功！
2025-08-13 01:50:23 - __main__ - INFO - 生成了 2 个集成模型
2025-08-13 01:50:23 - __main__ - INFO -   voting_soft: 准确率=0.9933, F1=0.9933
2025-08-13 01:50:23 - __main__ - INFO -   voting_hard: 准确率=0.9867, F1=0.9867
