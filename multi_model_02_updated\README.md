# 多模型二分类机器学习平台

这是一个集成了多种机器学习算法的二分类任务平台，支持超参数调优、模型集成、智能模型选择等功能。

## 功能特点

### 🎯 核心功能
- **多模型支持**: 支持10种主流机器学习算法
  - 决策树 (Decision Tree)
  - 随机森林 (Random Forest)
  - XGBoost
  - LightGBM
  - CatBoost
  - 逻辑回归 (Logistic Regression)
  - 支持向量机 (SVM)
  - K近邻 (KNN)
  - 朴素贝叶斯 (Naive Bayes)
  - 神经网络 (Neural Network)

- **超参数调优**: 基于Optuna的智能超参数优化
  - 支持多种优化策略（TPE、Random等）
  - 早停机制避免过拟合
  - 自动GPU加速检测

- **集成学习**: 多种集成方法
  - 投票法 (Voting)
  - 装袋法 (Bagging)
  - 提升法 (Boosting)
  - 堆叠法 (Stacking)

- **智能模型选择**: 基于性能和多样性的模型组合优化

### 🖥️ 用户界面
- **GUI模式**: 完整的图形用户界面，操作简单直观
- **命令行模式**: 适合自动化和批量处理
- **实时可视化**: 训练过程和结果实时展示

### 🔧 高级功能
- **多数据源集成**: 支持不同数据源的模型融合
- **SHAP解释性**: 模型决策的可解释性分析
- **会话管理**: 训练过程的会话化管理和恢复
- **DeLong检验**: 模型性能的统计显著性检验

## 快速开始

### 环境要求
- Python 3.8+
- 推荐使用conda虚拟环境

### 安装步骤

1. **创建并激活虚拟环境**
```bash
conda create -n multi_model python=3.9
conda activate multi_model
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

### 启动项目

#### 方法1: 使用启动脚本（推荐）
```bash
# Windows
start.bat

# 或使用Python脚本
python run.py
```

#### 方法2: 直接启动GUI
```bash
python gui_main.py
```

#### 方法3: 命令行模式
```bash
# 训练所有模型
python main.py --model All --mode train

# 训练指定模型
python main.py --model RandomForest,XGBoost --mode train

# 超参数调优
python main.py --model XGBoost --mode tune --n_trials 100

# 模型比较
python main.py --model All --mode compare
```

### 项目测试
运行项目功能测试：
```bash
python test_project.py
```

## 使用指南

### GUI模式使用
1. **数据准备**
   - 点击"加载数据"选择CSV文件
   - 系统自动检测目标列（label/target/class/y）
   - 支持数据预览和质量检查

2. **模型训练**
   - 选择要训练的模型
   - 配置训练参数（测试集比例、随机种子等）
   - 点击"开始训练"
   - 支持实时查看训练进度

3. **结果分析**
   - 单模型可视化：查看单个模型的详细性能
   - 模型比较：对比多个模型的性能指标
   - DeLong检验：统计显著性比较

4. **集成学习**
   - 选择基础模型和集成方法
   - 配置智能选择策略
   - 运行集成并查看结果

### 命令行参数详解

```bash
python main.py [参数]

必需参数:
  --model MODEL         选择模型 (All 或 逗号分隔的模型名)
  
可选参数:
  --mode MODE           运行模式
                        train: 训练模型
                        tune: 超参数调优
                        plot: 可视化
                        compare: 模型比较
                        report: 生成报告
                        ensemble: 集成学习
  
  --data PATH           数据文件路径
  --n_trials N          调优试验次数 (默认: 50)
  --strict_reproducibility  启用严格复现模式
  
集成学习参数:
  --feature_selection METHOD  特征选择方法
  --ensemble_methods METHODS 集成方法列表
```

## 项目结构

```
multi_model_02_updated/
├── code/                    # 核心代码目录
│   ├── config.py           # 配置管理
│   ├── data_preprocessing.py # 数据预处理
│   ├── model_training.py   # 模型训练
│   ├── hyperparameter_tuning.py # 超参数调优
│   ├── model_ensemble.py   # 集成学习
│   ├── multi_data_ensemble.py # 多数据源集成
│   └── ...
├── gui_main.py             # GUI主程序
├── main.py                 # 命令行主程序
├── run.py                  # 启动脚本
├── test_project.py         # 测试脚本
├── start.bat              # Windows启动脚本
└── requirements.txt        # 依赖列表
```

## 数据格式要求

### 输入数据格式
- CSV文件
- 最后一列或名为label/target/class/y的列作为目标变量
- 其他列作为特征
- 支持数值型和类别型特征

### 示例数据格式
```csv
feature1,feature2,feature3,target
1.2,3.4,5.6,0
2.1,4.3,6.5,1
...
```

## 常见问题

### Q: GPU加速如何启用？
A: 系统会自动检测GPU可用性。如果安装了GPU版本的XGBoost/CatBoost，会自动使用GPU。

### Q: 训练很慢怎么办？
A: 可以：
- 减少n_trials参数
- 使用strict_reproducibility模式限制线程数
- 选择较少的模型进行训练

### Q: 如何保存和加载模型？
A: 所有训练的模型会自动保存到cache目录和training_sessions目录。

### Q: GUI界面中文显示异常？
A: 系统会自动使用英文字体避免中文显示问题。

## 更新日志

### v2.0 (当前版本)
- 新增GPU自动检测和优化
- 改进线程安全性
- 修复超参数调优中的问题
- 增强集成学习功能
- 优化Windows兼容性

## 技术支持

如有问题，请检查：
1. Python版本是否符合要求
2. 依赖是否正确安装
3. 运行测试脚本：`python test_project.py`