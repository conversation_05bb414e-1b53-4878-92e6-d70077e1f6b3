# Quantified策略多样性指标表格导出功能使用指南

## 功能概述

在使用quantified策略进行模型组合选择时，系统会自动计算并导出详细的多样性指标表格，包括：

- **Q统计量多样性**: 衡量模型间统计相关性的多样性
- **不一致性多样性**: 模型预测不一致的程度
- **双错多样性**: 模型互补性的度量
- **相关性多样性**: 预测结果线性相关性的多样性
- **综合得分**: 结合性能和多样性的最终评分

## 导出文件说明

### 1. 模型对多样性指标表格 (`pairwise_diversity_metrics_*.csv`)

包含所有模型对的详细多样性指标：

| 列名 | 说明 | 取值范围 |
|------|------|----------|
| 模型对 | 模型对名称 | - |
| Q统计量 | 原始Q统计量值 | [-1, 1] |
| Q统计量多样性 | 1 - \|Q统计量\| | [0, 1] |
| 不一致性度量 | 预测不一致的样本比例 | [0, 1] |
| 双错度量 | 都预测错误的样本比例 | [0, 1] |
| 双错多样性 | 1 - 双错度量 | [0, 1] |
| 相关系数 | Pearson相关系数 | [-1, 1] |
| 相关性多样性 | 1 - \|相关系数\| | [0, 1] |
| 综合多样性得分 | 加权综合多样性得分 | [0, 1] |
| 多样性等级 | 优秀/良好/中等/较差 | - |

### 2. 组合评估结果表格 (`combination_evaluation_results_*.csv`)

包含所有模型组合的综合评估结果：

| 列名 | 说明 |
|------|------|
| 模型组合 | 组合中的模型名称 |
| 组合大小 | 组合中模型的数量 |
| 平均性能得分 | 组合中模型的平均性能 |
| 平均Q统计量多样性 | 组合内模型对的平均Q统计量多样性 |
| 平均不一致性 | 组合内模型对的平均不一致性 |
| 平均双错多样性 | 组合内模型对的平均双错多样性 |
| 平均相关性多样性 | 组合内模型对的平均相关性多样性 |
| 综合多样性得分 | 组合的综合多样性得分 |
| 综合得分 | 性能和多样性的加权综合得分 |
| 多样性等级 | 多样性质量等级 |
| 推荐集成方法 | 基于多样性得分推荐的集成方法 |

### 3. 综合分析报告 (`diversity_analysis_report_*.txt`)

包含：
- 模型个体性能概览
- 多样性指标统计信息
- 整体多样性质量评估
- 改进建议

## 使用方法

### 方法1: 命令行使用

```bash
# 基本用法（使用默认导出目录）
python code/main.py --model RandomForest,DecisionTree,LogisticRegression,SVM \
                    --mode ensemble \
                    --data your_data.csv

# 指定自定义导出目录
python code/main.py --model RandomForest,DecisionTree,LogisticRegression,SVM \
                    --mode ensemble \
                    --data your_data.csv \
                    --diversity_export_dir ./my_diversity_analysis
```

### 方法2: 编程接口使用

```python
from code.enhanced_ensemble_selector import EnhancedEnsembleSelector
from code.quantified_diversity_evaluator import QuantifiedDiversityEvaluator

# 创建选择器实例
selector = EnhancedEnsembleSelector()

# 设置模型结果和性能得分
selector.model_results = your_model_results
selector.performance_scores = your_performance_scores
selector.current_y_test = y_test

# 导出多样性分析表格
exported_files = selector.export_quantified_diversity_analysis(
    qualified_models=['RandomForest', 'DecisionTree', 'LogisticRegression'],
    target_size=3,
    export_dir='./custom_export_directory'
)
```

### 方法3: 直接使用评估器

```python
from code.quantified_diversity_evaluator import QuantifiedDiversityEvaluator
from itertools import combinations

# 创建评估器
evaluator = QuantifiedDiversityEvaluator()

# 生成模型组合
model_names = ['RandomForest', 'DecisionTree', 'LogisticRegression']
all_combinations = []
for size in range(2, len(model_names) + 1):
    all_combinations.extend(list(combinations(model_names, size)))

# 导出表格
exported_files = evaluator.export_diversity_tables(
    y_true=y_test,
    predictions_dict=predictions_dict,
    performance_scores=performance_scores,
    combinations=all_combinations,
    diversity_weight=0.5,
    export_dir='./diversity_analysis'
)
```

## 多样性指标解释

### Q统计量 (Q-statistic)
- **计算公式**: Q = (N₁₁×N₀₀ - N₀₁×N₁₀) / (N₁₁×N₀₀ + N₀₁×N₁₀)
- **含义**: 衡量两个分类器预测结果的统计相关性
- **多样性解释**: Q越接近0，多样性越大

### 不一致性度量 (Disagreement Measure)
- **计算公式**: 预测不一致的样本数 / 总样本数
- **含义**: 两个模型预测不一致的程度
- **多样性解释**: 值越大，多样性越大

### 双错度量 (Double-fault Measure)
- **计算公式**: 都预测错误的样本数 / 总样本数
- **含义**: 两个模型都预测错误的程度
- **多样性解释**: 值越小，互补性越强，多样性越大

### 相关系数 (Correlation Coefficient)
- **计算公式**: Pearson相关系数
- **含义**: 预测结果的线性相关性
- **多样性解释**: 绝对值越小，多样性越大

## 综合得分计算

```
综合多样性得分 = 0.3 × Q统计量多样性 + 
                0.3 × 不一致性度量 + 
                0.2 × 双错多样性 + 
                0.2 × 相关性多样性

最终综合得分 = α × 性能得分 + β × 多样性得分
```

其中 α + β = 1，默认 α = β = 0.5

## 多样性等级划分

- **优秀** (≥0.7): 推荐简单投票法
- **良好** (0.5-0.7): 推荐加权平均
- **中等** (0.3-0.5): 推荐堆叠法
- **较差** (<0.3): 建议重新选择模型

## 注意事项

1. **数据要求**: 需要有训练好的模型预测结果和真实标签
2. **组合数量**: 组合数量随模型数量指数增长，建议控制在合理范围内
3. **计算时间**: 大量组合的多样性计算可能需要较长时间
4. **文件编码**: 导出的CSV文件使用UTF-8-BOM编码，确保中文正确显示

## 示例输出

运行示例后，你将在指定目录中看到：

```
diversity_analysis/
├── pairwise_diversity_metrics_20250823_113154.csv
├── combination_evaluation_results_20250823_113154.csv
└── diversity_analysis_report_20250823_113154.txt
```

这些文件提供了完整的多样性分析结果，帮助你：
- 理解模型间的多样性关系
- 选择最优的模型组合
- 确定合适的集成方法
- 识别需要改进的方面
