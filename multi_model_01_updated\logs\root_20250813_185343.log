2025-08-13 18:53:43 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 20
2025-08-13 18:53:43 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: -1
2025-08-13 18:53:43 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-13 18:53:43 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 5，最小改善: 0.001
2025-08-13 18:53:43 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 20, 'n_jobs': 1}
2025-08-13 18:53:43 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-13 18:53:44 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.6861
2025-08-13 18:53:44 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-13 18:53:45 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-13 18:53:45 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-13 18:53:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-13 18:53:47 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.6895
2025-08-13 18:53:47 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-13 18:53:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-13 18:53:49 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.7095
2025-08-13 18:53:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-13 18:53:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-13 18:53:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-13 18:53:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-13 18:53:55 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-13 18:53:57 - hyperparameter_tuning - INFO - 早停触发：连续 5 轮没有显著改善（阈值: 0.001）
2025-08-13 18:53:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7095
2025-08-13 18:53:57 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 202, 'max_depth': 3, 'learning_rate': 0.02886496196573106, 'subsample': 0.9744427686266666, 'colsample_bytree': 0.9828160165372797}
2025-08-13 18:53:57 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.7095
2025-08-13 18:53:57 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/20
2025-08-13 18:53:57 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-13 18:53:58 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-13 18:53:58 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-13 18:53:58 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250813_185358.html
2025-08-13 18:53:58 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-13 18:53:58 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-13 18:53:58 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250813_185358.html
2025-08-13 18:53:58 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 14.77 秒
2025-08-13 18:53:58 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-13 18:54:00 - model_training - INFO - [XGBoost] 启用GPU加速
2025-08-13 18:54:00 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.045 以处理不平衡
2025-08-13 18:54:00 - model_training - INFO - 模型名称: XGBoost
2025-08-13 18:54:00 - model_training - INFO - 准确率: 0.6188
2025-08-13 18:54:00 - model_training - INFO - AUC: 0.6549
2025-08-13 18:54:00 - model_training - INFO - AUPRC: 0.6211
2025-08-13 18:54:00 - model_training - INFO - 混淆矩阵:
2025-08-13 18:54:00 - model_training - INFO - 
[[47 34]
 [27 52]]
2025-08-13 18:54:00 - model_training - INFO - 
分类报告:
2025-08-13 18:54:00 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.64      0.58      0.61        81
           1       0.60      0.66      0.63        79

    accuracy                           0.62       160
   macro avg       0.62      0.62      0.62       160
weighted avg       0.62      0.62      0.62       160

2025-08-13 18:54:00 - model_training - INFO - 训练时间: 1.95 秒
2025-08-13 18:54:00 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.6188
2025-08-13 18:54:00 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-13 18:54:00 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-13 18:54:00 - model_training - INFO - 模型名称: Random Forest
2025-08-13 18:54:00 - model_training - INFO - 准确率: 0.6625
2025-08-13 18:54:00 - model_training - INFO - AUC: 0.7046
2025-08-13 18:54:00 - model_training - INFO - AUPRC: 0.6821
2025-08-13 18:54:00 - model_training - INFO - 混淆矩阵:
2025-08-13 18:54:00 - model_training - INFO - 
[[50 31]
 [23 56]]
2025-08-13 18:54:00 - model_training - INFO - 
分类报告:
2025-08-13 18:54:00 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.68      0.62      0.65        81
           1       0.64      0.71      0.67        79

    accuracy                           0.66       160
   macro avg       0.66      0.66      0.66       160
weighted avg       0.66      0.66      0.66       160

2025-08-13 18:54:00 - model_training - INFO - 训练时间: 0.19 秒
2025-08-13 18:54:00 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.6625
2025-08-13 18:54:00 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-13 18:54:00 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-13 18:54:00 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.045 以处理不平衡
2025-08-13 18:54:00 - model_training - INFO - 模型名称: LightGBM
2025-08-13 18:54:00 - model_training - INFO - 准确率: 0.6250
2025-08-13 18:54:00 - model_training - INFO - AUC: 0.6764
2025-08-13 18:54:00 - model_training - INFO - AUPRC: 0.6620
2025-08-13 18:54:00 - model_training - INFO - 混淆矩阵:
2025-08-13 18:54:00 - model_training - INFO - 
[[46 35]
 [25 54]]
2025-08-13 18:54:00 - model_training - INFO - 
分类报告:
2025-08-13 18:54:00 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.65      0.57      0.61        81
           1       0.61      0.68      0.64        79

    accuracy                           0.62       160
   macro avg       0.63      0.63      0.62       160
weighted avg       0.63      0.62      0.62       160

2025-08-13 18:54:00 - model_training - INFO - 训练时间: 0.07 秒
2025-08-13 18:54:00 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.6250
2025-08-13 18:54:00 - model_training - INFO - 模型 LightGBM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-13 18:54:00 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-08-13 18:54:01 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-13 18:54:01 - model_ensemble - INFO - ============================================================
2025-08-13 18:54:01 - model_ensemble - INFO - 开始运行集成学习管道
2025-08-13 18:54:01 - model_ensemble - INFO - ============================================================
2025-08-13 18:54:01 - model_ensemble - INFO - 基础模型: ['XGBoost', 'RandomForest', 'LightGBM']
2025-08-13 18:54:01 - model_ensemble - INFO - 集成方法: ['voting', 'bagging']
2025-08-13 18:54:01 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-08-13 18:54:01 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-08-13 18:54:01 - model_training - INFO - [XGBoost] 启用GPU加速
2025-08-13 18:54:01 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.000 以处理不平衡
2025-08-13 18:54:01 - model_training - INFO - 模型名称: XGBoost
2025-08-13 18:54:01 - model_training - INFO - 准确率: 0.5750
2025-08-13 18:54:01 - model_training - INFO - AUC: 0.6117
2025-08-13 18:54:01 - model_training - INFO - AUPRC: 0.5596
2025-08-13 18:54:01 - model_training - INFO - 混淆矩阵:
2025-08-13 18:54:01 - model_training - INFO - 
[[36 30]
 [21 33]]
2025-08-13 18:54:01 - model_training - INFO - 
分类报告:
2025-08-13 18:54:01 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.63      0.55      0.59        66
           1       0.52      0.61      0.56        54

    accuracy                           0.57       120
   macro avg       0.58      0.58      0.57       120
weighted avg       0.58      0.57      0.58       120

2025-08-13 18:54:01 - model_training - INFO - 训练时间: 0.18 秒
2025-08-13 18:54:01 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.5750
2025-08-13 18:54:01 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-13 18:54:01 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-13 18:54:01 - model_ensemble - INFO -   XGBoost 训练完成
2025-08-13 18:54:01 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-08-13 18:54:01 - model_training - INFO - 模型名称: Random Forest
2025-08-13 18:54:01 - model_training - INFO - 准确率: 0.6167
2025-08-13 18:54:01 - model_training - INFO - AUC: 0.6748
2025-08-13 18:54:01 - model_training - INFO - AUPRC: 0.6221
2025-08-13 18:54:01 - model_training - INFO - 混淆矩阵:
2025-08-13 18:54:01 - model_training - INFO - 
[[38 28]
 [18 36]]
2025-08-13 18:54:01 - model_training - INFO - 
分类报告:
2025-08-13 18:54:01 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.68      0.58      0.62        66
           1       0.56      0.67      0.61        54

    accuracy                           0.62       120
   macro avg       0.62      0.62      0.62       120
weighted avg       0.63      0.62      0.62       120

2025-08-13 18:54:01 - model_training - INFO - 训练时间: 0.18 秒
2025-08-13 18:54:01 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.6167
2025-08-13 18:54:01 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-13 18:54:01 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-13 18:54:01 - model_ensemble - INFO -   RandomForest 训练完成
2025-08-13 18:54:01 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-08-13 18:54:01 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.000 以处理不平衡
2025-08-13 18:54:01 - model_training - INFO - 模型名称: LightGBM
2025-08-13 18:54:01 - model_training - INFO - 准确率: 0.6333
2025-08-13 18:54:01 - model_training - INFO - AUC: 0.6510
2025-08-13 18:54:01 - model_training - INFO - AUPRC: 0.6074
2025-08-13 18:54:01 - model_training - INFO - 混淆矩阵:
2025-08-13 18:54:01 - model_training - INFO - 
[[41 25]
 [19 35]]
2025-08-13 18:54:01 - model_training - INFO - 
分类报告:
2025-08-13 18:54:01 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.68      0.62      0.65        66
           1       0.58      0.65      0.61        54

    accuracy                           0.63       120
   macro avg       0.63      0.63      0.63       120
weighted avg       0.64      0.63      0.63       120

2025-08-13 18:54:01 - model_training - INFO - 训练时间: 0.05 秒
2025-08-13 18:54:01 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.6333
2025-08-13 18:54:01 - model_training - INFO - 模型 LightGBM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-13 18:54:01 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-08-13 18:54:01 - model_ensemble - INFO -   LightGBM 训练完成
2025-08-13 18:54:01 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-08-13 18:54:01 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-08-13 18:54:01 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-08-13 18:54:01 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 18:54:02 - model_ensemble - INFO - 集成模型 voting_soft 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_soft_results.joblib
2025-08-13 18:54:02 - model_ensemble - INFO - 集成模型 voting_soft 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_soft_feature_names.joblib
2025-08-13 18:54:02 - model_ensemble - INFO -     voting_soft - 准确率: 0.5833, F1: 0.5839
2025-08-13 18:54:02 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-08-13 18:54:02 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 18:54:02 - model_ensemble - INFO - 集成模型 voting_hard 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_hard_results.joblib
2025-08-13 18:54:02 - model_ensemble - INFO - 集成模型 voting_hard 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_hard_feature_names.joblib
2025-08-13 18:54:02 - model_ensemble - INFO -     voting_hard - 准确率: 0.6167, F1: 0.6175
2025-08-13 18:54:02 - model_ensemble - INFO - 步骤2: 运行集成方法 - bagging
2025-08-13 18:54:02 - model_ensemble - INFO - 开始训练集成模型，方法: bagging
2025-08-13 18:54:04 - model_ensemble - INFO - 集成模型 bagging 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_bagging_results.joblib
2025-08-13 18:54:04 - model_ensemble - INFO - 集成模型 bagging 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_bagging_feature_names.joblib
2025-08-13 18:54:04 - model_ensemble - INFO -   bagging - 准确率: 0.6250, F1: 0.6257
2025-08-13 18:54:04 - model_ensemble - INFO - ============================================================
2025-08-13 18:54:04 - model_ensemble - INFO - 集成学习结果总结
2025-08-13 18:54:04 - model_ensemble - INFO - ============================================================
2025-08-13 18:54:04 - model_ensemble - INFO - 最佳集成模型: bagging
2025-08-13 18:54:04 - model_ensemble - INFO - 最佳F1分数: 0.6257
2025-08-13 18:54:04 - model_ensemble - INFO - 最佳准确率: 0.6250
2025-08-13 18:54:04 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-08-13 18:54:04 - model_ensemble - INFO -   voting_soft     - 准确率: 0.5833, 精确率: 0.5926, 召回率: 0.5833, F1: 0.5839, AUC: 0.6479
2025-08-13 18:54:04 - model_ensemble - INFO -   voting_hard     - 准确率: 0.6167, 精确率: 0.6239, 召回率: 0.6167, F1: 0.6175, AUC: 0.6479
2025-08-13 18:54:04 - model_ensemble - INFO -   bagging         - 准确率: 0.6250, 精确率: 0.6335, 召回率: 0.6250, F1: 0.6257, AUC: 0.6754
