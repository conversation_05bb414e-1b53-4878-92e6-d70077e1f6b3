2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 DecisionTree
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 RandomForest
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 XGBoost
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 LightGBM
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 CatBoost
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 Logistic
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 SVM
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 KNN
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 NaiveBayes
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 NeuralNet
2025-09-02 23:45:54 - model_inference - INFO - 成功加载 10/10 个模型
2025-09-02 23:45:54 - prediction_api - INFO - API已加载 10 个模型
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 DecisionTree
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 RandomForest
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 XGBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 LightGBM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 CatBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 Logistic
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 SVM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 KNN
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NaiveBayes
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NeuralNet
2025-09-02 23:45:55 - model_inference - INFO - 成功加载 10/10 个模型
2025-09-02 23:45:55 - prediction_api - INFO - API已加载 10 个模型
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 1 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 1 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 2/2 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 DecisionTree
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 RandomForest
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 XGBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 LightGBM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 CatBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 Logistic
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 SVM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 KNN
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NaiveBayes
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NeuralNet
2025-09-02 23:45:55 - model_inference - INFO - 成功加载 10/10 个模型
2025-09-02 23:45:55 - prediction_api - INFO - API已加载 10 个模型
2025-09-02 23:45:55 - prediction_api - ERROR - 不支持的数据类型: <class 'list'>
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 DecisionTree
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 RandomForest
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 XGBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 LightGBM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 CatBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 Logistic
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 SVM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 KNN
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NaiveBayes
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NeuralNet
2025-09-02 23:45:55 - model_inference - INFO - 成功加载 10/10 个模型
2025-09-02 23:45:55 - prediction_api - INFO - API已加载 10 个模型
2025-09-02 23:45:55 - prediction_api - ERROR - 数据准备失败: load_and_preprocess_data() got an unexpected keyword argument 'target_column'
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 DecisionTree
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 RandomForest
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 XGBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 LightGBM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 CatBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 Logistic
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 SVM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 KNN
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NaiveBayes
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NeuralNet
2025-09-02 23:45:55 - model_inference - INFO - 成功加载 10/10 个模型
2025-09-02 23:45:55 - prediction_api - INFO - API已加载 10 个模型
2025-09-02 23:45:55 - batch_prediction - INFO - 开始读取数据: large_test_data.csv
2025-09-02 23:45:55 - batch_prediction - INFO - 数据集大小: 1000 样本
2025-09-02 23:45:55 - batch_prediction - INFO - 数据分为 5 个块进行处理
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 2/2 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 2/2 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 2/2 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 2/2 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 2/2 个模型的批量预测
2025-09-02 23:45:55 - batch_prediction - INFO - 批量预测完成，耗时 0.06 秒
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 DecisionTree
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 RandomForest
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 XGBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 LightGBM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 CatBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 Logistic
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 SVM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 KNN
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NaiveBayes
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NeuralNet
2025-09-02 23:45:55 - model_inference - INFO - 成功加载 10/10 个模型
2025-09-02 23:45:55 - prediction_api - INFO - API已加载 10 个模型
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 50 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 50 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 LightGBM 完成 50 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 3/3 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 5 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 5 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 LightGBM 完成 5 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 3/3 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 5 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 5 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 LightGBM 完成 5 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 3/3 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 3 个模型的集成预测
