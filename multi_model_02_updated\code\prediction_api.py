#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的预测接口
提供易用的API接口进行模型预测
"""

import numpy as np
import pandas as pd
from pathlib import Path
from typing import Union, Dict, List, Optional, Any
import json
import warnings
warnings.filterwarnings('ignore')

from model_inference import ModelInference
from data_preprocessing import load_and_preprocess_data
from config import MODEL_NAMES, MODEL_DISPLAY_NAMES
from logger import get_logger

logger = get_logger("prediction_api")


class PredictionAPI:
    """
    简化的预测API类
    提供高级的、易用的预测接口
    """
    
    def __init__(self, auto_load_models: bool = True):
        """
        初始化预测API
        
        Args:
            auto_load_models: 是否自动加载所有可用模型
        """
        self.inference = ModelInference()
        self.available_models = []
        
        if auto_load_models:
            self.load_all_models()
    
    def load_all_models(self) -> Dict[str, bool]:
        """加载所有可用模型"""
        results = self.inference.load_all_available_models()
        self.available_models = [name for name, success in results.items() if success]
        logger.info(f"API已加载 {len(self.available_models)} 个模型")
        return results
    
    def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        return self.available_models.copy()
    
    def predict_from_data(self, data: Union[str, pd.DataFrame, np.ndarray],
                         models: Optional[List[str]] = None,
                         return_proba: bool = True,
                         ensemble_method: Optional[str] = None) -> Dict[str, Any]:
        """
        从数据进行预测
        
        Args:
            data: 输入数据（文件路径、DataFrame或数组）
            models: 使用的模型列表，None表示使用所有可用模型
            return_proba: 是否返回预测概率
            ensemble_method: 集成方法，None表示不进行集成
            
        Returns:
            Dict: 预测结果和相关信息
        """
        try:
            # 数据预处理
            X = self._prepare_data(data)
            if X is None:
                return {"error": "数据预处理失败"}
            
            # 确定使用的模型
            if models is None:
                models = self.available_models
            else:
                # 验证模型是否可用
                models = [m for m in models if m in self.available_models]
                if not models:
                    return {"error": "没有可用的模型"}
            
            # 进行预测
            predictions = self.inference.predict_batch(models, X, return_proba)
            
            if not predictions:
                return {"error": "预测失败"}
            
            result = {
                "success": True,
                "predictions": predictions,
                "models_used": list(predictions.keys()),
                "sample_count": len(X),
                "prediction_type": "probability" if return_proba else "class"
            }
            
            # 集成预测
            if ensemble_method and len(predictions) > 1:
                ensemble_pred = self.inference.ensemble_predict(
                    list(predictions.keys()), X, ensemble_method
                )
                if ensemble_pred is not None:
                    result["ensemble_prediction"] = ensemble_pred
                    result["ensemble_method"] = ensemble_method
            
            return result
            
        except Exception as e:
            logger.error(f"预测过程出错: {e}")
            return {"error": str(e)}
    
    def predict_single_sample(self, sample: Union[List, np.ndarray, pd.Series],
                             models: Optional[List[str]] = None,
                             return_proba: bool = True) -> Dict[str, Any]:
        """
        预测单个样本
        
        Args:
            sample: 单个样本数据
            models: 使用的模型列表
            return_proba: 是否返回预测概率
            
        Returns:
            Dict: 预测结果
        """
        # 转换为二维数组
        if isinstance(sample, (list, pd.Series)):
            X = np.array(sample).reshape(1, -1)
        elif isinstance(sample, np.ndarray):
            X = sample.reshape(1, -1) if sample.ndim == 1 else sample
        else:
            return {"error": "不支持的样本格式"}
        
        result = self.predict_from_data(X, models, return_proba)
        
        # 提取单个样本的结果
        if result.get("success"):
            single_predictions = {}
            for model, preds in result["predictions"].items():
                single_predictions[model] = float(preds[0]) if len(preds) > 0 else None
            
            result["predictions"] = single_predictions
            if "ensemble_prediction" in result:
                result["ensemble_prediction"] = float(result["ensemble_prediction"][0])
        
        return result
    
    def predict_from_file(self, file_path: str,
                         models: Optional[List[str]] = None,
                         return_proba: bool = True,
                         save_results: bool = True,
                         output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        从文件进行预测
        
        Args:
            file_path: 数据文件路径
            models: 使用的模型列表
            return_proba: 是否返回预测概率
            save_results: 是否保存结果
            output_file: 输出文件路径
            
        Returns:
            Dict: 预测结果
        """
        result = self.predict_from_data(file_path, models, return_proba)
        
        if result.get("success") and save_results:
            if output_file is None:
                file_stem = Path(file_path).stem
                output_file = f"predictions_{file_stem}.json"
            
            # 保存结果
            if self._save_prediction_results(result, output_file):
                result["output_file"] = output_file
        
        return result
    
    def get_model_performance(self, model_name: str) -> Optional[Dict]:
        """
        获取模型性能信息
        
        Args:
            model_name: 模型名称
            
        Returns:
            Dict: 模型性能信息
        """
        return self.inference.get_model_info(model_name)
    
    def compare_models(self, data: Union[str, pd.DataFrame, np.ndarray],
                      models: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        比较多个模型的预测结果
        
        Args:
            data: 输入数据
            models: 要比较的模型列表
            
        Returns:
            Dict: 比较结果
        """
        result = self.predict_from_data(data, models, return_proba=True)
        
        if not result.get("success"):
            return result
        
        predictions = result["predictions"]
        
        # 计算统计信息
        comparison = {
            "model_count": len(predictions),
            "models": list(predictions.keys()),
            "statistics": {}
        }
        
        for model, preds in predictions.items():
            comparison["statistics"][model] = {
                "mean": float(np.mean(preds)),
                "std": float(np.std(preds)),
                "min": float(np.min(preds)),
                "max": float(np.max(preds)),
                "median": float(np.median(preds))
            }
        
        # 计算模型间相关性
        if len(predictions) > 1:
            pred_df = pd.DataFrame(predictions)
            correlation_matrix = pred_df.corr()
            comparison["correlation_matrix"] = correlation_matrix.to_dict()
        
        result["comparison"] = comparison
        return result
    
    def _prepare_data(self, data: Union[str, pd.DataFrame, np.ndarray]) -> Optional[np.ndarray]:
        """
        准备输入数据
        
        Args:
            data: 输入数据
            
        Returns:
            np.ndarray: 处理后的数据
        """
        try:
            if isinstance(data, str):
                # 从文件加载数据
                if not Path(data).exists():
                    logger.error(f"数据文件不存在: {data}")
                    return None
                
                # 使用现有的数据预处理函数
                X, _ = load_and_preprocess_data(data, target_column=None)
                return X
                
            elif isinstance(data, pd.DataFrame):
                return data.values
                
            elif isinstance(data, np.ndarray):
                return data
                
            else:
                logger.error(f"不支持的数据类型: {type(data)}")
                return None
                
        except Exception as e:
            logger.error(f"数据准备失败: {e}")
            return None
    
    def _save_prediction_results(self, results: Dict, output_file: str) -> bool:
        """
        保存预测结果
        
        Args:
            results: 预测结果
            output_file: 输出文件
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 转换numpy数组为列表以便JSON序列化
            json_results = results.copy()
            if "predictions" in json_results:
                json_results["predictions"] = {
                    k: v.tolist() if isinstance(v, np.ndarray) else v
                    for k, v in json_results["predictions"].items()
                }
            
            if "ensemble_prediction" in json_results:
                ensemble_pred = json_results["ensemble_prediction"]
                if isinstance(ensemble_pred, np.ndarray):
                    json_results["ensemble_prediction"] = ensemble_pred.tolist()
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(json_results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"预测结果已保存至: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存预测结果失败: {e}")
            return False


# 便捷函数
def predict(data: Union[str, pd.DataFrame, np.ndarray, List],
           models: Optional[List[str]] = None,
           return_proba: bool = True,
           ensemble_method: Optional[str] = None) -> Dict[str, Any]:
    """
    便捷预测函数
    
    Args:
        data: 输入数据
        models: 使用的模型列表
        return_proba: 是否返回预测概率
        ensemble_method: 集成方法
        
    Returns:
        Dict: 预测结果
    """
    api = PredictionAPI()
    
    # 判断是单个样本还是批量数据
    if isinstance(data, list) and len(data) > 0 and not isinstance(data[0], (list, np.ndarray)):
        # 单个样本
        return api.predict_single_sample(data, models, return_proba)
    else:
        # 批量数据
        return api.predict_from_data(data, models, return_proba, ensemble_method)


def predict_file(file_path: str,
                models: Optional[List[str]] = None,
                return_proba: bool = True,
                save_results: bool = True,
                output_file: Optional[str] = None) -> Dict[str, Any]:
    """
    从文件预测的便捷函数
    
    Args:
        file_path: 数据文件路径
        models: 使用的模型列表
        return_proba: 是否返回预测概率
        save_results: 是否保存结果
        output_file: 输出文件路径
        
    Returns:
        Dict: 预测结果
    """
    api = PredictionAPI()
    return api.predict_from_file(file_path, models, return_proba, save_results, output_file)


def get_available_models() -> List[str]:
    """获取可用模型列表的便捷函数"""
    api = PredictionAPI()
    return api.get_available_models()


if __name__ == "__main__":
    # 示例用法
    print("预测API示例")
    
    # 获取可用模型
    models = get_available_models()
    print(f"可用模型: {models}")
    
    # 示例：预测单个样本
    sample = [1.0, 2.0, 3.0, 4.0, 5.0]  # 示例特征
    result = predict(sample, models=models[:2] if models else None)
    print(f"单样本预测结果: {result}")
