<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="6">
            <item index="0" class="java.lang.String" itemvalue="nibabel" />
            <item index="1" class="java.lang.String" itemvalue="pydicom" />
            <item index="2" class="java.lang.String" itemvalue="torch" />
            <item index="3" class="java.lang.String" itemvalue="TotalSegmentator" />
            <item index="4" class="java.lang.String" itemvalue="black" />
            <item index="5" class="java.lang.String" itemvalue="flake8" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>