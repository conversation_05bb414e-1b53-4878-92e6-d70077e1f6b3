#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多模型二分类项目启动脚本
支持命令行和GUI两种模式
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
code_path = project_root / 'code'
sys.path.insert(0, str(code_path))

def run_cli_mode(args):
    """运行命令行模式"""
    print("启动命令行模式...")
    
    # 导入主程序
    try:
        from main import main as cli_main
        # 构建命令行参数
        sys.argv = ['main.py'] + args
        cli_main()
    except Exception as e:
        print(f"命令行模式启动失败: {e}")
        return False
    return True

def run_gui_mode():
    """运行GUI模式"""
    print("启动GUI模式...")
    
    try:
        # 导入GUI主程序
        gui_main_path = project_root / 'gui_main.py'
        if gui_main_path.exists():
            # 直接运行GUI主程序
            os.system(f'python "{gui_main_path}"')
        else:
            print("找不到GUI主程序文件")
            return False
    except Exception as e:
        print(f"GUI模式启动失败: {e}")
        return False
    return True

def run_test_mode():
    """运行测试模式"""
    print("运行项目测试...")
    
    test_script = project_root / 'test_project.py'
    if test_script.exists():
        os.system(f'python "{test_script}"')
    else:
        print("找不到测试脚本")
        return False
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="多模型二分类机器学习平台",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s                    # 启动GUI模式（默认）
  %(prog)s --cli              # 启动命令行模式
  %(prog)s --cli --model All  # 训练所有模型
  %(prog)s --test             # 运行项目测试
  %(prog)s --help             # 显示帮助信息
        """
    )
    
    # 模式选择
    group = parser.add_mutually_exclusive_group()
    group.add_argument('--cli', action='store_true', 
                      help='使用命令行模式')
    group.add_argument('--test', action='store_true',
                      help='运行项目测试')
    
    # 如果没有参数，默认显示帮助
    if len(sys.argv) == 1:
        parser.print_help()
        print("\n默认启动GUI模式...")
        return run_gui_mode()
    
    # 解析参数
    args, remaining_args = parser.parse_known_args()
    
    # 根据模式运行
    if args.test:
        return run_test_mode()
    elif args.cli:
        return run_cli_mode(remaining_args)
    else:
        return run_gui_mode()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)