2025-08-13 02:02:38 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-13 02:02:38 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-13 02:02:38 - __main__ - INFO - ================================================================================
2025-08-13 02:02:38 - __main__ - INFO - 测试统一集成结果对象功能
2025-08-13 02:02:38 - __main__ - INFO - ================================================================================
2025-08-13 02:02:38 - __main__ - INFO - 训练集大小: (210, 8)
2025-08-13 02:02:38 - __main__ - INFO - 测试集大小: (90, 8)
2025-08-13 02:02:38 - __main__ - INFO - 开始测试集成学习管道...
2025-08-13 02:02:38 - model_ensemble - INFO - ============================================================
2025-08-13 02:02:38 - model_ensemble - INFO - 开始运行集成学习管道
2025-08-13 02:02:38 - model_ensemble - INFO - ============================================================
2025-08-13 02:02:38 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-08-13 02:02:38 - model_ensemble - INFO - 集成方法: ['voting', 'bagging']
2025-08-13 02:02:38 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-08-13 02:02:38 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-08-13 02:02:38 - model_training - INFO - 模型名称: Random Forest
2025-08-13 02:02:38 - model_training - INFO - 准确率: 0.9889
2025-08-13 02:02:38 - model_training - INFO - AUC: 0.9990
2025-08-13 02:02:38 - model_training - INFO - AUPRC: 0.9990
2025-08-13 02:02:38 - model_training - INFO - 混淆矩阵:
2025-08-13 02:02:38 - model_training - INFO - 
[[46  0]
 [ 1 43]]
2025-08-13 02:02:38 - model_training - INFO - 
分类报告:
2025-08-13 02:02:38 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.98      1.00      0.99        46
           1       1.00      0.98      0.99        44

    accuracy                           0.99        90
   macro avg       0.99      0.99      0.99        90
weighted avg       0.99      0.99      0.99        90

2025-08-13 02:02:38 - model_training - INFO - 训练时间: 0.09 秒
2025-08-13 02:02:38 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9889
2025-08-13 02:02:38 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-13 02:02:38 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-13 02:02:38 - model_ensemble - INFO -   RandomForest 训练完成
2025-08-13 02:02:38 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-08-13 02:02:38 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.019 以处理不平衡
2025-08-13 02:02:38 - model_training - INFO - 模型名称: XGBoost
2025-08-13 02:02:38 - model_training - INFO - 准确率: 0.9889
2025-08-13 02:02:38 - model_training - INFO - AUC: 1.0000
2025-08-13 02:02:38 - model_training - INFO - AUPRC: 1.0000
2025-08-13 02:02:38 - model_training - INFO - 混淆矩阵:
2025-08-13 02:02:38 - model_training - INFO - 
[[46  0]
 [ 1 43]]
2025-08-13 02:02:38 - model_training - INFO - 
分类报告:
2025-08-13 02:02:38 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.98      1.00      0.99        46
           1       1.00      0.98      0.99        44

    accuracy                           0.99        90
   macro avg       0.99      0.99      0.99        90
weighted avg       0.99      0.99      0.99        90

2025-08-13 02:02:38 - model_training - INFO - 训练时间: 0.09 秒
2025-08-13 02:02:38 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9889
2025-08-13 02:02:38 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-13 02:02:38 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-13 02:02:38 - model_ensemble - INFO -   XGBoost 训练完成
2025-08-13 02:02:38 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-08-13 02:02:38 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-08-13 02:02:38 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-08-13 02:02:38 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 02:02:38 - model_ensemble - INFO - 集成模型 voting_soft 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_soft_results.joblib
2025-08-13 02:02:38 - model_ensemble - INFO - 集成模型 voting_soft 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_soft_feature_names.joblib
2025-08-13 02:02:38 - model_ensemble - INFO -     voting_soft - 准确率: 0.9889, F1: 0.9889
2025-08-13 02:02:38 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-08-13 02:02:38 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 02:02:39 - model_ensemble - INFO - 集成模型 voting_hard 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_hard_results.joblib
2025-08-13 02:02:39 - model_ensemble - INFO - 集成模型 voting_hard 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_hard_feature_names.joblib
2025-08-13 02:02:39 - model_ensemble - INFO -     voting_hard - 准确率: 0.9889, F1: 0.9889
2025-08-13 02:02:39 - model_ensemble - INFO - 步骤2: 运行集成方法 - bagging
2025-08-13 02:02:39 - model_ensemble - INFO - 开始训练集成模型，方法: bagging
2025-08-13 02:02:40 - model_ensemble - INFO - 集成模型 bagging 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_bagging_results.joblib
2025-08-13 02:02:40 - model_ensemble - INFO - 集成模型 bagging 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_bagging_feature_names.joblib
2025-08-13 02:02:40 - model_ensemble - INFO -   bagging - 准确率: 0.9667, F1: 0.9667
2025-08-13 02:02:40 - model_ensemble - INFO - ============================================================
2025-08-13 02:02:40 - model_ensemble - INFO - 集成学习结果总结
2025-08-13 02:02:40 - model_ensemble - INFO - ============================================================
2025-08-13 02:02:40 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-08-13 02:02:40 - model_ensemble - INFO - 最佳F1分数: 0.9889
2025-08-13 02:02:40 - model_ensemble - INFO - 最佳准确率: 0.9889
2025-08-13 02:02:40 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-08-13 02:02:40 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9889, 精确率: 0.9891, 召回率: 0.9889, F1: 0.9889, AUC: 1.0000
2025-08-13 02:02:40 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9889, 精确率: 0.9891, 召回率: 0.9889, F1: 0.9889, AUC: 0.0000
2025-08-13 02:02:40 - model_ensemble - INFO -   bagging         - 准确率: 0.9667, 精确率: 0.9669, 召回率: 0.9667, F1: 0.9667, AUC: 0.9960
2025-08-13 02:02:40 - __main__ - INFO - ✅ 集成学习管道测试成功！
2025-08-13 02:02:40 - __main__ - INFO - 生成了 3 个集成模型
2025-08-13 02:02:40 - __main__ - INFO - 
验证新增指标:
2025-08-13 02:02:40 - __main__ - INFO -   voting_soft:
2025-08-13 02:02:40 - __main__ - INFO -     准确率: 0.9889
2025-08-13 02:02:40 - __main__ - INFO -     F1分数: 0.9889
2025-08-13 02:02:40 - __main__ - INFO -     AUC: 1.0000
2025-08-13 02:02:40 - __main__ - INFO -     AUPRC: 1.0
2025-08-13 02:02:40 - __main__ - INFO -     平衡准确率: 0.9886363636363636
2025-08-13 02:02:40 - __main__ - INFO -     Brier分数: 0.0180431904629584
2025-08-13 02:02:40 - __main__ - INFO -     MCC: 0.9779977967880316
2025-08-13 02:02:40 - __main__ - INFO -   voting_hard:
2025-08-13 02:02:40 - __main__ - INFO -     准确率: 0.9889
2025-08-13 02:02:40 - __main__ - INFO -     F1分数: 0.9889
2025-08-13 02:02:40 - __main__ - INFO -     AUC: 0.0000
2025-08-13 02:02:40 - __main__ - INFO -     AUPRC: 0.0
2025-08-13 02:02:40 - __main__ - INFO -     平衡准确率: 0.9886363636363636
2025-08-13 02:02:40 - __main__ - INFO -     Brier分数: None
2025-08-13 02:02:40 - __main__ - INFO -     MCC: 0.9779977967880316
2025-08-13 02:02:40 - __main__ - INFO -   bagging:
2025-08-13 02:02:40 - __main__ - INFO -     准确率: 0.9667
2025-08-13 02:02:40 - __main__ - INFO -     F1分数: 0.9667
2025-08-13 02:02:40 - __main__ - INFO -     AUC: 0.9960
2025-08-13 02:02:40 - __main__ - INFO -     AUPRC: 0.9957204058885609
2025-08-13 02:02:40 - __main__ - INFO -     平衡准确率: 0.966897233201581
2025-08-13 02:02:40 - __main__ - INFO -     Brier分数: 0.043183577777777774
2025-08-13 02:02:40 - __main__ - INFO -     MCC: 0.9335638713962128
2025-08-13 02:02:40 - __main__ - INFO - 
验证缓存文件:
2025-08-13 02:02:40 - __main__ - INFO -   ✅ voting_soft 结果缓存文件存在
2025-08-13 02:02:40 - __main__ - INFO -     ✅ 缓存结构完整
2025-08-13 02:02:40 - __main__ - INFO -     ✅ 模型类型标记正确: ensemble
2025-08-13 02:02:40 - __main__ - INFO -   ✅ voting_soft 特征名称缓存文件存在
2025-08-13 02:02:40 - __main__ - INFO -   ✅ voting_hard 结果缓存文件存在
2025-08-13 02:02:40 - __main__ - INFO -     ✅ 缓存结构完整
2025-08-13 02:02:40 - __main__ - INFO -     ✅ 模型类型标记正确: ensemble
2025-08-13 02:02:40 - __main__ - INFO -   ✅ voting_hard 特征名称缓存文件存在
2025-08-13 02:02:40 - __main__ - INFO -   ✅ bagging 结果缓存文件存在
2025-08-13 02:02:40 - __main__ - INFO -     ✅ 缓存结构完整
2025-08-13 02:02:40 - __main__ - INFO -     ✅ 模型类型标记正确: ensemble
2025-08-13 02:02:40 - __main__ - INFO -   ✅ bagging 特征名称缓存文件存在
2025-08-13 02:02:40 - __main__ - INFO - 
测试GUI显示格式兼容性:
2025-08-13 02:02:40 - __main__ - INFO -   模拟GUI性能表格显示:
2025-08-13 02:02:40 - __main__ - INFO -   模型名称            准确率      精确率      召回率      F1分数     AUC      AUPRC    Bal_Acc  Brier    MCC     
2025-08-13 02:02:40 - __main__ - INFO -   --------------------------------------------------------------------------------------------------------------
2025-08-13 02:02:40 - __main__ - INFO -   🏆 voting_soft  0.9889   0.9891   0.9889   0.9889   1.0000   1.0000   0.9886   0.0180   0.9780  
2025-08-13 02:02:40 - __main__ - INFO -    2 voting_hard  0.9889   0.9891   0.9889   0.9889   0.0000   0.0000   0.9886   N/A      0.9780  
2025-08-13 02:02:40 - __main__ - INFO -    3 bagging      0.9667   0.9669   0.9667   0.9667   0.9960   0.9957   0.9669   0.0432   0.9336  
2025-08-13 02:02:40 - __main__ - INFO -   ✅ GUI显示格式测试通过
2025-08-13 02:02:40 - __main__ - INFO - 
清理测试缓存文件:
2025-08-13 02:02:40 - __main__ - INFO -   ✅ 已清理: Ensemble_voting_soft_results.joblib
2025-08-13 02:02:40 - __main__ - INFO -   ✅ 已清理: Ensemble_voting_hard_results.joblib
2025-08-13 02:02:40 - __main__ - INFO -   ✅ 已清理: Ensemble_bagging_results.joblib
