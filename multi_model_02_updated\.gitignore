# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Project specific
logs/
models/
output/
plots/
cache/
catboost_info/
multi_data_cache/
training_sessions/
reports/
*.log
*.pdf
*.xlsx
*.csv

# OS
.DS_Store
Thumbs.db

# Git
.git
.gitignore