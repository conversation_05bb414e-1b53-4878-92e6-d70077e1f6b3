2025-08-13 21:41:00 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-13 21:41:00 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-13 21:41:00 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-13 21:41:01 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-13 21:41:01 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-13 21:41:01 - GUI - INFO - GUI界面初始化完成
2025-08-13 21:41:20 - config_manager - INFO - 默认配置已初始化
2025-08-13 21:41:20 - config_manager - INFO - 注册预处理器: current_scaler (StandardScaler)
2025-08-13 21:41:20 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-13 21:41:20 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-13 21:41:20 - enhanced_ensemble_selector - ERROR - 评估模型 DecisionTree 失败: __init__() got an unexpected keyword argument 'n_jobs'
2025-08-13 21:41:20 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-13 21:41:20 - model_training - INFO - 模型名称: Random Forest
2025-08-13 21:41:20 - model_training - INFO - 准确率: 0.8500
2025-08-13 21:41:20 - model_training - INFO - AUC: 0.9501
2025-08-13 21:41:20 - model_training - INFO - AUPRC: 0.9410
2025-08-13 21:41:20 - model_training - INFO - 混淆矩阵:
2025-08-13 21:41:20 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-13 21:41:20 - model_training - INFO - 
分类报告:
2025-08-13 21:41:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-13 21:41:20 - model_training - INFO - 训练时间: 0.20 秒
2025-08-13 21:41:20 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-13 21:41:20 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-13 21:41:20 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-13 21:41:20 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8759
2025-08-13 21:41:20 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-13 21:41:20 - model_training - WARNING - [XGBoost] PyTorch未安装，无法检测GPU，使用CPU模式
2025-08-13 21:41:20 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-13 21:41:20 - model_training - INFO - 模型名称: XGBoost
2025-08-13 21:41:20 - model_training - INFO - 准确率: 0.8250
2025-08-13 21:41:20 - model_training - INFO - AUC: 0.9437
2025-08-13 21:41:20 - model_training - INFO - AUPRC: 0.9387
2025-08-13 21:41:20 - model_training - INFO - 混淆矩阵:
2025-08-13 21:41:20 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-13 21:41:20 - model_training - INFO - 
分类报告:
2025-08-13 21:41:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-13 21:41:21 - model_training - INFO - 训练时间: 0.04 秒
2025-08-13 21:41:21 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-13 21:41:21 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-13 21:41:21 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-13 21:41:21 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8563
2025-08-13 21:41:21 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-13 21:41:21 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-13 21:41:21 - model_training - INFO - 模型名称: LightGBM
2025-08-13 21:41:21 - model_training - INFO - 准确率: 0.8750
2025-08-13 21:41:21 - model_training - INFO - AUC: 0.9642
2025-08-13 21:41:21 - model_training - INFO - AUPRC: 0.9541
2025-08-13 21:41:21 - model_training - INFO - 混淆矩阵:
2025-08-13 21:41:21 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-13 21:41:21 - model_training - INFO - 
分类报告:
2025-08-13 21:41:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-13 21:41:21 - model_training - INFO - 训练时间: 0.07 秒
2025-08-13 21:41:21 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-13 21:41:21 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-13 21:41:21 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-08-13 21:41:21 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8989
2025-08-13 21:41:21 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-13 21:41:21 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-13 21:41:22 - model_training - INFO - 模型名称: CatBoost
2025-08-13 21:41:22 - model_training - INFO - 准确率: 0.8000
2025-08-13 21:41:22 - model_training - INFO - AUC: 0.9540
2025-08-13 21:41:22 - model_training - INFO - AUPRC: 0.9486
2025-08-13 21:41:22 - model_training - INFO - 混淆矩阵:
2025-08-13 21:41:22 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-13 21:41:22 - model_training - INFO - 
分类报告:
2025-08-13 21:41:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-13 21:41:22 - model_training - INFO - 训练时间: 1.20 秒
2025-08-13 21:41:22 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8000
2025-08-13 21:41:22 - model_training - INFO - 模型 CatBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-13 21:41:22 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_feature_names.joblib
2025-08-13 21:41:22 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8395
2025-08-13 21:41:22 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-13 21:41:24 - model_training - INFO - 模型名称: Logistic Regression
2025-08-13 21:41:24 - model_training - INFO - 准确率: 0.7750
2025-08-13 21:41:24 - model_training - INFO - AUC: 0.9028
2025-08-13 21:41:24 - model_training - INFO - AUPRC: 0.8685
2025-08-13 21:41:24 - model_training - INFO - 混淆矩阵:
2025-08-13 21:41:24 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-13 21:41:24 - model_training - INFO - 
分类报告:
2025-08-13 21:41:24 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-13 21:41:24 - model_training - INFO - 训练时间: 2.07 秒
2025-08-13 21:41:24 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-13 21:41:24 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-13 21:41:24 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-13 21:41:24 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8087
2025-08-13 21:41:24 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-13 21:41:24 - model_training - INFO - 模型名称: SVM
2025-08-13 21:41:24 - model_training - INFO - 准确率: 0.8250
2025-08-13 21:41:24 - model_training - INFO - AUC: 0.9258
2025-08-13 21:41:24 - model_training - INFO - AUPRC: 0.9259
2025-08-13 21:41:24 - model_training - INFO - 混淆矩阵:
2025-08-13 21:41:24 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-13 21:41:24 - model_training - INFO - 
分类报告:
2025-08-13 21:41:24 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-13 21:41:24 - model_training - INFO - 训练时间: 0.01 秒
2025-08-13 21:41:24 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-13 21:41:24 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-13 21:41:24 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-08-13 21:41:24 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8537
2025-08-13 21:41:24 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-13 21:41:28 - model_training - INFO - 模型名称: KNN
2025-08-13 21:41:28 - model_training - INFO - 准确率: 0.8000
2025-08-13 21:41:28 - model_training - INFO - AUC: 0.9054
2025-08-13 21:41:28 - model_training - INFO - AUPRC: 0.8760
2025-08-13 21:41:28 - model_training - INFO - 混淆矩阵:
2025-08-13 21:41:28 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-13 21:41:28 - model_training - INFO - 
分类报告:
2025-08-13 21:41:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-13 21:41:28 - model_training - INFO - 训练时间: 4.23 秒
2025-08-13 21:41:28 - model_training - INFO - 模型 KNN 性能: 准确率=0.8000
2025-08-13 21:41:28 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-13 21:41:28 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-08-13 21:41:28 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8273
2025-08-13 21:41:28 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-08-13 21:41:28 - model_training - INFO - 模型名称: Naive Bayes
2025-08-13 21:41:28 - model_training - INFO - 准确率: 0.8500
2025-08-13 21:41:28 - model_training - INFO - AUC: 0.9156
2025-08-13 21:41:28 - model_training - INFO - AUPRC: 0.8954
2025-08-13 21:41:28 - model_training - INFO - 混淆矩阵:
2025-08-13 21:41:28 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-13 21:41:28 - model_training - INFO - 
分类报告:
2025-08-13 21:41:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-13 21:41:28 - model_training - INFO - 训练时间: 0.01 秒
2025-08-13 21:41:28 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8500
2025-08-13 21:41:28 - model_training - INFO - 模型 NaiveBayes 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-13 21:41:28 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_feature_names.joblib
2025-08-13 21:41:28 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8664
2025-08-13 21:41:28 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-08-13 21:41:28 - model_training - INFO - 模型名称: Neural Network
2025-08-13 21:41:28 - model_training - INFO - 准确率: 0.8500
2025-08-13 21:41:28 - model_training - INFO - AUC: 0.9437
2025-08-13 21:41:28 - model_training - INFO - AUPRC: 0.9159
2025-08-13 21:41:28 - model_training - INFO - 混淆矩阵:
2025-08-13 21:41:28 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-13 21:41:28 - model_training - INFO - 
分类报告:
2025-08-13 21:41:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-13 21:41:28 - model_training - INFO - 训练时间: 0.24 秒
2025-08-13 21:41:28 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8500
2025-08-13 21:41:28 - model_training - INFO - 模型 NeuralNet 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-13 21:41:28 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_feature_names.joblib
2025-08-13 21:41:28 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8743
2025-08-13 21:41:28 - improved_diversity_analyzer - INFO - 开始综合多样性分析...
2025-08-13 21:41:29 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.239
2025-08-13 21:41:29 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-08-13 21:41:29 - improved_diversity_analyzer - INFO - 综合多样性得分: 0.382
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.239
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.382
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.916, 多样性=0.084
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.928, 多样性=0.072
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.979, 多样性=0.021
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.943, 多样性=0.057
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.929, 多样性=0.071
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.873, 多样性=0.127
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.868, 多样性=0.132
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.900, 多样性=0.100
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.927, 多样性=0.073
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.970, 多样性=0.030
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.834, 多样性=0.166
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.810, 多样性=0.190
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.766, 多样性=0.234
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.761, 多样性=0.239
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.784, 多样性=0.216
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.948, 多样性=0.052
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.855, 多样性=0.145
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.833, 多样性=0.167
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.773, 多样性=0.227
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.748, 多样性=0.252
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.806, 多样性=0.194
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.904, 多样性=0.096
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.898, 多样性=0.102
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.828, 多样性=0.172
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.838, 多样性=0.162
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.868, 多样性=0.132
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.953, 多样性=0.047
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.881, 多样性=0.119
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.824, 多样性=0.176
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.928, 多样性=0.072
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.890, 多样性=0.110
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.876, 多样性=0.124
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.924, 多样性=0.076
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.903, 多样性=0.097
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.841, 多样性=0.159
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.851, 多样性=0.149
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.392
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 4, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 9 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.235
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.970 (多样性: 0.030)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.241
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.205
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.799 (多样性: 0.201)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.210
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.951 (多样性: 0.049)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.895 (多样性: 0.105)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.311
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.326
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.812 (多样性: 0.188)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.359
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.377
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.378
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.227
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.309
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.842 (多样性: 0.158)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.337
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.367
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.408
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.410
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.883 (多样性: 0.117)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.701 (多样性: 0.299)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.322
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.905 (多样性: 0.095)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.199
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.759 (多样性: 0.241)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.276
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.778 (多样性: 0.222)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.904 (多样性: 0.096)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.204
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.286
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.060
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.410
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.286
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.211
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   熵多样性: 0.222
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO - 从 9 个合格模型中选择 3 个进行集成
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始评估 84 个模型组合...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.235
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.970 (多样性: 0.030)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.241
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.210
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.229
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.013
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.210
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.241
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'XGBoost', 'LightGBM'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.877
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.229
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.553
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.235
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.205
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.951 (多样性: 0.049)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.207
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.021
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.235
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'XGBoost', 'CatBoost'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.857
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.207
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.532
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.235
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.895 (多样性: 0.105)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.311
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.256
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.039
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.311
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'XGBoost', 'Logistic'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.847
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.256
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.552
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.235
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.326
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.265
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.044
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.326
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'XGBoost', 'SVM'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.862
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.265
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.563
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.235
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.812 (多样性: 0.188)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.359
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.305
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.052
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.235
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.359
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'XGBoost', 'KNN'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.853
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.305
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.579
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.235
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.377
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.293
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.061
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.235
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.377
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'XGBoost', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.866
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.293
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.580
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.235
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.799 (多样性: 0.201)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.378
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.294
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.061
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.235
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.378
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'XGBoost', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.869
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.294
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.581
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.970 (多样性: 0.030)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.241
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.205
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.227
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.225
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.015
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.205
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.241
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'LightGBM', 'CatBoost'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.871
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.225
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.548
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.970 (多样性: 0.030)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.241
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.309
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.258
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.037
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.309
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'LightGBM', 'Logistic'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.861
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.258
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.559
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.970 (多样性: 0.030)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.241
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.842 (多样性: 0.158)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.337
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.270
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.047
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.337
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'LightGBM', 'SVM'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.876
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.270
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.573
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.970 (多样性: 0.030)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.241
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.367
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.310
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.052
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.241
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.367
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'LightGBM', 'KNN'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.867
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.310
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.589
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.970 (多样性: 0.030)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.241
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.408
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.306
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.073
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.241
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.408
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'LightGBM', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.880
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.306
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.593
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.970 (多样性: 0.030)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.241
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.799 (多样性: 0.201)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.410
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.306
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.074
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.241
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.410
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'LightGBM', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.883
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.306
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.595
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.205
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.235
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.031
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.205
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'CatBoost', 'Logistic'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.841
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.235
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.538
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.205
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.241
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.033
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.205
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'CatBoost', 'SVM'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.856
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.241
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.548
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.205
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.883 (多样性: 0.117)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.281
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.054
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.205
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'CatBoost', 'KNN'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.848
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.281
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.564
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.205
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.701 (多样性: 0.299)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.322
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.265
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.048
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.205
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.322
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'CatBoost', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.861
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.265
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.563
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.205
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.799 (多样性: 0.201)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.265
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.048
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.205
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'CatBoost', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.863
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.265
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.564
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.905 (多样性: 0.095)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.199
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.218
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.014
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.199
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'Logistic', 'SVM'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.846
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.218
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.532
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.274
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.041
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'Logistic', 'KNN'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.837
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.274
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.556
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.759 (多样性: 0.241)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.276
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.255
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.024
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.276
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'Logistic', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.850
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.255
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.553
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.799 (多样性: 0.201)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.238
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.022
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'Logistic', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.853
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.238
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.545
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.280
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.037
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'SVM', 'KNN'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.852
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.280
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.566
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.778 (多样性: 0.222)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.263
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.023
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'SVM', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.865
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.263
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.564
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.799 (多样性: 0.201)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.245
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.017
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'SVM', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.868
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.245
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.556
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.904 (多样性: 0.096)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.204
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.265
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.048
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.204
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'KNN', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.857
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.265
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.561
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.799 (多样性: 0.201)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.305
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.026
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'KNN', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.859
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.305
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.582
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.799 (多样性: 0.201)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.000
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('RandomForest', 'NaiveBayes', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.872
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.570
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.210
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.951 (多样性: 0.049)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.227
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.207
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.019
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.227
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'LightGBM', 'CatBoost'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.865
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.207
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.536
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.210
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.895 (多样性: 0.105)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.311
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.309
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.047
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.210
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.311
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'LightGBM', 'Logistic'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.855
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.566
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.210
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.326
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.842 (多样性: 0.158)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.337
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.291
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.057
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.210
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.337
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'LightGBM', 'SVM'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.870
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.291
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.580
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.210
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.812 (多样性: 0.188)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.359
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.367
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.312
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.072
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.210
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.367
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'LightGBM', 'KNN'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.861
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.312
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.586
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.210
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.377
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.408
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.332
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.087
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.210
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.408
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'LightGBM', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.874
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.332
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.603
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.210
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.378
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.410
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.333
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.088
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.210
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.410
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'LightGBM', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.877
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.333
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.605
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.951 (多样性: 0.049)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.895 (多样性: 0.105)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.311
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.257
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.055
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.311
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'CatBoost', 'Logistic'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.835
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.257
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.546
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.951 (多样性: 0.049)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.326
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.264
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.060
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.326
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'CatBoost', 'SVM'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.850
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.264
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.557
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.951 (多样性: 0.049)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.812 (多样性: 0.188)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.359
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.883 (多样性: 0.117)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.285
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.359
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'CatBoost', 'KNN'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.841
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.285
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.563
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.951 (多样性: 0.049)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.377
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.701 (多样性: 0.299)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.322
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.294
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.082
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.377
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'CatBoost', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.854
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.294
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.574
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.951 (多样性: 0.049)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.378
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.294
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.082
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.378
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'CatBoost', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.857
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.294
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.576
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.895 (多样性: 0.105)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.311
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.326
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.905 (多样性: 0.095)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.199
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.279
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.057
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.199
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.326
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'Logistic', 'SVM'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.840
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.279
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.559
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.895 (多样性: 0.105)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.311
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.812 (多样性: 0.188)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.359
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.316
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.033
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.359
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'Logistic', 'KNN'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.831
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.316
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.573
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.895 (多样性: 0.105)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.311
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.377
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.759 (多样性: 0.241)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.276
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.321
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.042
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.276
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.377
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'Logistic', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.844
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.321
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.583
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.895 (多样性: 0.105)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.311
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.378
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.304
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.064
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.378
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'Logistic', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.846
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.304
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.575
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.326
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.812 (多样性: 0.188)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.359
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.031
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.359
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'SVM', 'KNN'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.846
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.584
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.326
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.377
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.778 (多样性: 0.222)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.331
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.036
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.377
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'SVM', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.859
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.331
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.595
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.326
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.378
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.312
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.060
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.378
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'SVM', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.861
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.312
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.587
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.812 (多样性: 0.188)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.359
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.377
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.904 (多样性: 0.096)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.204
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.313
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.077
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.204
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.377
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'KNN', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.850
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.313
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.582
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.812 (多样性: 0.188)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.359
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.378
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.353
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.023
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.378
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'KNN', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.853
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.353
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.603
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.377
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.378
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.341
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.052
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.378
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('XGBoost', 'NaiveBayes', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.866
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.341
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.603
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.227
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.309
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.271
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.034
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.227
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.309
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'CatBoost', 'Logistic'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.849
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.271
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.560
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.227
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.842 (多样性: 0.158)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.337
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.283
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.045
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.227
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.337
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'CatBoost', 'SVM'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.864
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.283
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.573
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.227
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.367
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.883 (多样性: 0.117)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.303
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.058
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.227
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.367
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'CatBoost', 'KNN'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.855
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.303
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.579
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.227
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.408
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.701 (多样性: 0.299)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.322
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.319
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.074
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.227
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.408
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'CatBoost', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.868
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.319
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.594
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.227
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.410
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.320
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.074
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.227
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.410
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'CatBoost', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.871
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.320
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.595
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.309
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.842 (多样性: 0.158)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.337
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.905 (多样性: 0.095)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.199
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.282
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.059
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.199
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.337
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'Logistic', 'SVM'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.854
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.282
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.568
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.309
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.367
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.318
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.037
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.367
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'Logistic', 'KNN'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.845
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.318
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.581
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.309
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.408
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.759 (多样性: 0.241)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.276
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.331
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.056
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.276
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.408
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'Logistic', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.858
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.331
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.595
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.309
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.410
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.314
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.077
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.410
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'Logistic', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.861
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.314
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.587
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.842 (多样性: 0.158)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.337
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.367
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.329
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.035
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.367
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'SVM', 'KNN'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.860
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.329
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.595
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.842 (多样性: 0.158)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.337
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.408
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.778 (多样性: 0.222)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.345
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.049
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.408
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'SVM', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.873
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.345
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.609
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.842 (多样性: 0.158)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.337
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.410
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.326
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.073
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.410
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'SVM', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.876
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.326
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.601
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.367
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.408
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.904 (多样性: 0.096)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.204
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.327
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.088
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.204
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.408
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'KNN', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.864
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.327
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.595
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.367
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.410
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.367
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.036
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.410
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'KNN', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.867
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.367
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.617
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.408
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.410
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.362
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.067
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.410
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('LightGBM', 'NaiveBayes', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.880
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.362
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.621
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.905 (多样性: 0.095)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.199
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.253
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.038
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.199
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'Logistic', 'SVM'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.834
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.253
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.544
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.883 (多样性: 0.117)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.018
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.315
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'Logistic', 'KNN'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.825
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.558
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.701 (多样性: 0.299)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.322
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.759 (多样性: 0.241)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.276
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.292
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.021
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.276
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.322
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'Logistic', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.838
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.292
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.565
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.274
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.041
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'Logistic', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.841
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.274
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.557
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.883 (多样性: 0.117)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.294
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.015
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.315
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'SVM', 'KNN'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.840
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.294
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.567
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.701 (多样性: 0.299)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.322
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.778 (多样性: 0.222)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.299
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.017
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.322
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'SVM', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.853
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.299
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.576
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.280
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.037
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'SVM', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.856
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.280
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.568
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.883 (多样性: 0.117)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.701 (多样性: 0.299)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.322
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.904 (多样性: 0.096)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.204
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.281
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.054
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.204
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.322
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'KNN', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.844
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.281
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.562
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.883 (多样性: 0.117)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.320
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.004
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.315
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'KNN', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.847
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.320
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.584
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.701 (多样性: 0.299)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.322
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.304
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.026
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('CatBoost', 'NaiveBayes', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.860
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.304
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.582
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.905 (多样性: 0.095)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.199
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.253
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.038
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.199
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('Logistic', 'SVM', 'KNN'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.830
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.253
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.542
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.905 (多样性: 0.095)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.199
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.759 (多样性: 0.241)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.276
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.778 (多样性: 0.222)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.255
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.040
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.199
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('Logistic', 'SVM', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.843
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.255
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.549
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.905 (多样性: 0.095)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.199
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.218
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.014
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.199
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('Logistic', 'SVM', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.846
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.218
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.532
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.759 (多样性: 0.241)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.276
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.904 (多样性: 0.096)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.204
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.253
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.034
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.204
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('Logistic', 'KNN', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.834
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.253
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.543
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.274
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.041
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('Logistic', 'KNN', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.837
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.274
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.555
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.759 (多样性: 0.241)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.276
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.255
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.024
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.276
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('Logistic', 'NaiveBayes', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.850
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.255
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.553
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.778 (多样性: 0.222)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.904 (多样性: 0.096)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.204
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.259
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.039
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.204
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('SVM', 'KNN', 'NaiveBayes'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.849
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.259
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.554
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.280
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.037
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('SVM', 'KNN', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.852
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.280
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.566
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.778 (多样性: 0.222)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.263
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.023
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('SVM', 'NaiveBayes', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.865
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.263
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.564
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 3 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.904 (多样性: 0.096)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.204
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.265
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.048
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.204
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   组合 ('KNN', 'NaiveBayes', 'NeuralNet'):
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     性能得分: 0.856
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性得分: 0.265
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合得分: 0.560
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     多样性等级: 较差的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 最优组合排序完成，共评估 84 个组合
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   最优组合: ['LightGBM', 'NaiveBayes', 'NeuralNet']
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   性能得分: 0.8799
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   多样性得分: 0.3619
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   综合得分: 0.6209
2025-08-13 21:41:29 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 开始计算 9 个模型间的量化多样性指标...
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.235
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.970 (多样性: 0.030)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.241
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.205
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.799 (多样性: 0.201)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.900 (多样性: 0.100)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.210
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.951 (多样性: 0.049)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.895 (多样性: 0.105)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.311
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.326
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.812 (多样性: 0.188)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.359
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.377
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.378
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.227
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.920 (多样性: 0.080)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.309
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.842 (多样性: 0.158)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.704 (多样性: 0.296)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.337
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.367
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.657 (多样性: 0.343)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.408
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.676 (多样性: 0.324)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.410
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.883 (多样性: 0.117)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.315
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.701 (多样性: 0.299)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.322
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.905 (多样性: 0.095)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.199
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.751 (多样性: 0.249)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.277
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.948 (多样性: 0.052)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.759 (多样性: 0.241)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.276
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.851 (多样性: 0.149)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.222
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.923 (多样性: 0.077)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.760 (多样性: 0.240)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.284
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.908 (多样性: 0.092)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.778 (多样性: 0.222)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.290
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.975 (多样性: 0.025)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.860 (多样性: 0.140)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.233
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.904 (多样性: 0.096)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.204
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.875 (多样性: 0.125)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.699 (多样性: 0.301)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.323
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     Q统计量: 0.939 (多样性: 0.061)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     相关系数: 0.803 (多样性: 0.197)
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -     综合多样性: 0.268
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   平均多样性: 0.286
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.060
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最小多样性: 0.182
2025-08-13 21:41:29 - quantified_diversity_evaluator - INFO -   最大多样性: 0.410
2025-08-13 21:41:46 - config_manager - INFO - 注册预处理器: current_scaler (StandardScaler)
2025-08-13 21:41:46 - model_ensemble - INFO - ============================================================
2025-08-13 21:41:46 - model_ensemble - INFO - 开始运行集成学习管道
2025-08-13 21:41:46 - model_ensemble - INFO - ============================================================
2025-08-13 21:41:46 - model_ensemble - INFO - 基础模型: ['LightGBM', 'NaiveBayes', 'NeuralNet']
2025-08-13 21:41:46 - model_ensemble - INFO - 集成方法: ['boosting']
2025-08-13 21:41:46 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-08-13 21:41:46 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-08-13 21:41:46 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-13 21:41:46 - model_training - INFO - 模型名称: LightGBM
2025-08-13 21:41:46 - model_training - INFO - 准确率: 0.8750
2025-08-13 21:41:46 - model_training - INFO - AUC: 0.9642
2025-08-13 21:41:46 - model_training - INFO - AUPRC: 0.9541
2025-08-13 21:41:46 - model_training - INFO - 混淆矩阵:
2025-08-13 21:41:46 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-13 21:41:46 - model_training - INFO - 
分类报告:
2025-08-13 21:41:46 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-13 21:41:46 - model_training - INFO - 训练时间: 0.03 秒
2025-08-13 21:41:46 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-13 21:41:46 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-13 21:41:46 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-08-13 21:41:46 - model_ensemble - INFO -   LightGBM 训练完成
2025-08-13 21:41:46 - model_ensemble - INFO - 训练基础模型: NaiveBayes
2025-08-13 21:41:46 - model_training - INFO - 模型名称: Naive Bayes
2025-08-13 21:41:46 - model_training - INFO - 准确率: 0.8500
2025-08-13 21:41:46 - model_training - INFO - AUC: 0.9156
2025-08-13 21:41:46 - model_training - INFO - AUPRC: 0.8954
2025-08-13 21:41:46 - model_training - INFO - 混淆矩阵:
2025-08-13 21:41:46 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-13 21:41:46 - model_training - INFO - 
分类报告:
2025-08-13 21:41:46 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-13 21:41:46 - model_training - INFO - 训练时间: 0.01 秒
2025-08-13 21:41:46 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8500
2025-08-13 21:41:46 - model_training - INFO - 模型 NaiveBayes 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-13 21:41:46 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_feature_names.joblib
2025-08-13 21:41:46 - model_ensemble - INFO -   NaiveBayes 训练完成
2025-08-13 21:41:46 - model_ensemble - INFO - 训练基础模型: NeuralNet
2025-08-13 21:41:47 - model_training - INFO - 模型名称: Neural Network
2025-08-13 21:41:47 - model_training - INFO - 准确率: 0.8500
2025-08-13 21:41:47 - model_training - INFO - AUC: 0.9437
2025-08-13 21:41:47 - model_training - INFO - AUPRC: 0.9159
2025-08-13 21:41:47 - model_training - INFO - 混淆矩阵:
2025-08-13 21:41:47 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-13 21:41:47 - model_training - INFO - 
分类报告:
2025-08-13 21:41:47 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-13 21:41:47 - model_training - INFO - 训练时间: 0.24 秒
2025-08-13 21:41:47 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8500
2025-08-13 21:41:47 - model_training - INFO - 模型 NeuralNet 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-13 21:41:47 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_feature_names.joblib
2025-08-13 21:41:47 - model_ensemble - INFO -   NeuralNet 训练完成
2025-08-13 21:41:47 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-08-13 21:41:47 - model_ensemble - INFO - 步骤2: 运行集成方法 - boosting
2025-08-13 21:41:47 - model_ensemble - INFO -     正在优化 boosting 的参数...
2025-08-13 21:41:54 - model_ensemble - INFO - AdaBoost参数优化完成: 最优CV f1_weighted=0.9355
2025-08-13 21:41:54 - model_ensemble - INFO -     使用优化参数: {'n_estimators': 200, 'learning_rate': 1.0}
2025-08-13 21:41:54 - model_ensemble - INFO - 开始训练集成模型，方法: boosting
2025-08-13 21:41:54 - model_ensemble - INFO - 集成模型 boosting 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_boosting_results.joblib
2025-08-13 21:41:54 - model_ensemble - INFO - 集成模型 boosting 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_boosting_feature_names.joblib
2025-08-13 21:41:54 - model_ensemble - INFO -   boosting - 准确率: 0.8750, F1: 0.8754
2025-08-13 21:41:54 - model_ensemble - INFO - ============================================================
2025-08-13 21:41:54 - model_ensemble - INFO - 集成学习结果总结
2025-08-13 21:41:54 - model_ensemble - INFO - ============================================================
2025-08-13 21:41:54 - model_ensemble - INFO - 最佳集成模型: boosting
2025-08-13 21:41:54 - model_ensemble - INFO - 最佳F1分数: 0.8754
2025-08-13 21:41:54 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-08-13 21:41:54 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-08-13 21:41:54 - model_ensemble - INFO -   boosting        - 准确率: 0.8750, 精确率: 0.8769, 召回率: 0.8750, F1: 0.8754, AUC: 0.9565
2025-08-13 21:41:54 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-08-13 21:41:54 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250813_214154.joblib
2025-08-13 21:41:54 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-08-13 21:41:54 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_summary_report.txt
2025-08-13 21:41:54 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-08-13 21:41:54 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-08-13 21:41:54 - model_ensemble - INFO - 为 boosting 生成SHAP分析
2025-08-13 21:41:55 - enhanced_shap_visualization - WARNING - TreeExplainer failed: Model type not yet supported by TreeExplainer: <class 'sklearn.ensemble._weight_boosting.AdaBoostClassifier'>, falling back to KernelExplainer
2025-08-13 21:41:55 - enhanced_shap_visualization - INFO - 使用通用KernelExplainer
2025-08-13 21:41:55 - shap - INFO - num_full_subsets = 2
2025-08-13 21:41:55 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:41:55 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:41:55 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:41:55 - shap - INFO - np.sum(w_aug) = 16.0
2025-08-13 21:41:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:41:55 - shap - INFO - phi = array([ 0.02998918,  0.03453163,  0.        , -0.01731991,  0.01154382,
        0.04302757,  0.        , -0.00825384,  0.02278579,  0.        ,
       -0.01910631,  0.        ,  0.0266604 ,  0.01132879,  0.        ,
        0.        ])
2025-08-13 21:41:55 - shap - INFO - np.sum(w_aug) = 16.0
2025-08-13 21:41:55 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:41:55 - shap - INFO - phi = array([-0.02998918, -0.03453163,  0.        ,  0.01731991, -0.01154382,
       -0.04302757,  0.        ,  0.00825384, -0.02278579,  0.        ,
        0.01910631,  0.        , -0.0266604 , -0.01132879,  0.        ,
        0.        ])
2025-08-13 21:41:55 - shap - INFO - num_full_subsets = 2
2025-08-13 21:41:55 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:41:55 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:41:55 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:41:56 - shap - INFO - np.sum(w_aug) = 16.0
2025-08-13 21:41:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:41:56 - shap - INFO - phi = array([-0.03995196, -0.02460284, -0.01377137,  0.07256188,  0.        ,
       -0.0292749 ,  0.        , -0.00926411,  0.02189777,  0.02983146,
        0.        ,  0.        , -0.02567664,  0.0105594 ,  0.        ,
        0.        ])
2025-08-13 21:41:56 - shap - INFO - np.sum(w_aug) = 16.0
2025-08-13 21:41:56 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:41:56 - shap - INFO - phi = array([ 0.03995196,  0.02460284,  0.01377137, -0.07256188,  0.        ,
        0.0292749 ,  0.        ,  0.00926411, -0.02189777, -0.02983146,
        0.        ,  0.        ,  0.02567664, -0.0105594 ,  0.        ,
        0.        ])
2025-08-13 21:41:56 - shap - INFO - num_full_subsets = 2
2025-08-13 21:41:56 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:41:56 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:41:56 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:41:57 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:41:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:41:57 - shap - INFO - phi = array([-0.05660971, -0.02184472,  0.        , -0.01629235, -0.00258108,
       -0.08377253,  0.        , -0.00707909,  0.        , -0.04634242,
        0.        , -0.00329203,  0.01394843,  0.        , -0.00492089,
        0.        ])
2025-08-13 21:41:57 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:41:57 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:41:57 - shap - INFO - phi = array([ 0.05660971,  0.02184472,  0.        ,  0.01629235,  0.00258108,
        0.08377253,  0.        ,  0.00707909,  0.        ,  0.04634242,
        0.        ,  0.00329203, -0.01394843,  0.        ,  0.00492089,
        0.        ])
2025-08-13 21:41:57 - shap - INFO - num_full_subsets = 2
2025-08-13 21:41:57 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:41:57 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:41:57 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:41:58 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:41:58 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:41:58 - shap - INFO - phi = array([-0.05505346, -0.02067874,  0.        , -0.01548825, -0.00817057,
       -0.08103039,  0.        ,  0.        , -0.05403077, -0.04451955,
        0.        ,  0.        ,  0.02506906, -0.00936368,  0.00805267,
        0.        ])
2025-08-13 21:41:58 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:41:58 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:41:58 - shap - INFO - phi = array([ 0.05505346,  0.02067874,  0.        ,  0.01548825,  0.00817057,
        0.08103039,  0.        ,  0.        ,  0.05403077,  0.04451955,
        0.        ,  0.        , -0.02506906,  0.00936368, -0.00805267,
        0.        ])
2025-08-13 21:41:58 - shap - INFO - num_full_subsets = 2
2025-08-13 21:41:58 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:41:58 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:41:58 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:41:59 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:41:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:41:59 - shap - INFO - phi = array([ 0.08099269, -0.02149223, -0.01244922,  0.07112957,  0.        ,
        0.02273003,  0.        ,  0.00243629,  0.02218047,  0.03076351,
        0.        ,  0.        ,  0.        , -0.00793027,  0.00715563,
        0.        ])
2025-08-13 21:41:59 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:41:59 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:41:59 - shap - INFO - phi = array([-0.08099269,  0.02149223,  0.01244922, -0.07112957,  0.        ,
       -0.02273003,  0.        , -0.00243629, -0.02218047, -0.03076351,
        0.        ,  0.        ,  0.        ,  0.00793027, -0.00715563,
        0.        ])
2025-08-13 21:41:59 - shap - INFO - num_full_subsets = 2
2025-08-13 21:41:59 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:41:59 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:41:59 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:00 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:00 - shap - INFO - phi = array([ 0.08205124,  0.01264725,  0.        , -0.01712389, -0.01048861,
        0.04248277,  0.        , -0.00823328,  0.02292205,  0.03100067,
       -0.01902586,  0.        ,  0.        ,  0.        ,  0.        ,
       -0.00709328])
2025-08-13 21:42:00 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:00 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:00 - shap - INFO - phi = array([-0.08205124, -0.01264725,  0.        ,  0.01712389,  0.01048861,
       -0.04248277,  0.        ,  0.00823328, -0.02292205, -0.03100067,
        0.01902586,  0.        ,  0.        ,  0.        ,  0.        ,
        0.00709328])
2025-08-13 21:42:00 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:00 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:00 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:00 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:01 - shap - INFO - np.sum(w_aug) = 16.0
2025-08-13 21:42:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:01 - shap - INFO - phi = array([ 0.0809128 ,  0.01272211,  0.        , -0.01661183,  0.01133278,
        0.04226788,  0.        ,  0.        ,  0.        ,  0.03090619,
        0.01572521,  0.        ,  0.02574945,  0.00610137,  0.        ,
       -0.00665007])
2025-08-13 21:42:01 - shap - INFO - np.sum(w_aug) = 16.0
2025-08-13 21:42:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:01 - shap - INFO - phi = array([-0.0809128 , -0.01272211,  0.        ,  0.01661183, -0.01133278,
       -0.04226788,  0.        ,  0.        ,  0.        , -0.03090619,
       -0.01572521,  0.        , -0.02574945, -0.00610137,  0.        ,
        0.00665007])
2025-08-13 21:42:01 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:01 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:01 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:01 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:02 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:02 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:02 - shap - INFO - phi = array([-0.02901316, -0.02285243,  0.        , -0.01725705, -0.01012295,
        0.04340314,  0.        ,  0.00227185,  0.02369539,  0.03188237,
        0.        ,  0.        ,  0.        ,  0.        ,  0.01382517,
        0.00286277])
2025-08-13 21:42:02 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:02 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:02 - shap - INFO - phi = array([ 0.02901316,  0.02285243,  0.        ,  0.01725705,  0.01012295,
       -0.04340314,  0.        , -0.00227185, -0.02369539, -0.03188237,
        0.        ,  0.        ,  0.        ,  0.        , -0.01382517,
       -0.00286277])
2025-08-13 21:42:02 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:02 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:02 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:02 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:03 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:03 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:03 - shap - INFO - phi = array([-0.05829721, -0.02256747,  0.        , -0.0165892 , -0.00243783,
        0.03965933,  0.        ,  0.        ,  0.        , -0.04788469,
       -0.01820103, -0.00345983, -0.02296212,  0.        , -0.00486507,
        0.        ])
2025-08-13 21:42:03 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:03 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:03 - shap - INFO - phi = array([ 0.05829721,  0.02256747,  0.        ,  0.0165892 ,  0.00243783,
       -0.03965933,  0.        ,  0.        ,  0.        ,  0.04788469,
        0.01820103,  0.00345983,  0.02296212,  0.        ,  0.00486507,
        0.        ])
2025-08-13 21:42:03 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:03 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:03 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:03 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:04 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:04 - shap - INFO - phi = array([-0.04527764,  0.01153705,  0.        , -0.01841627,  0.01069252,
        0.02085345,  0.        ,  0.        ,  0.0212268 ,  0.03039082,
        0.01593453,  0.        ,  0.00797013,  0.01129201,  0.        ,
        0.        ])
2025-08-13 21:42:04 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:04 - shap - INFO - phi = array([ 0.04527764, -0.01153705,  0.        ,  0.01841627, -0.01069252,
       -0.02085345,  0.        ,  0.        , -0.0212268 , -0.03039082,
       -0.01593453,  0.        , -0.00797013, -0.01129201,  0.        ,
        0.        ])
2025-08-13 21:42:04 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:04 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:04 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:04 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:05 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:05 - shap - INFO - phi = array([ 0.08106811,  0.03343066, -0.01218678,  0.07121927,  0.        ,
        0.02240516,  0.        ,  0.        , -0.01065234,  0.        ,
        0.02081957,  0.00564411, -0.02345348,  0.00593632,  0.        ,
        0.        ])
2025-08-13 21:42:05 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:05 - shap - INFO - phi = array([-0.08106811, -0.03343066,  0.01218678, -0.07121927,  0.        ,
       -0.02240516,  0.        ,  0.        ,  0.01065234,  0.        ,
       -0.02081957, -0.00564411,  0.02345348, -0.00593632,  0.        ,
        0.        ])
2025-08-13 21:42:05 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:05 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:05 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:05 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:06 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:06 - shap - INFO - phi = array([ 0.02995724,  0.01281455,  0.        , -0.01667479,  0.0113813 ,
        0.04282744,  0.        , -0.00816396,  0.0226839 ,  0.03133321,
        0.02109109,  0.        ,  0.        ,  0.        ,  0.        ,
        0.00196328])
2025-08-13 21:42:06 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:06 - shap - INFO - phi = array([-0.02995724, -0.01281455,  0.        ,  0.01667479, -0.0113813 ,
       -0.04282744,  0.        ,  0.00816396, -0.0226839 , -0.03133321,
       -0.02109109,  0.        ,  0.        ,  0.        ,  0.        ,
       -0.00196328])
2025-08-13 21:42:06 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:06 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:06 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:06 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:07 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:07 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:42:07 - shap - INFO - phi = array([-0.05584154, -0.02169966,  0.        , -0.01633478, -0.00852633,
       -0.08344806,  0.        ,  0.        ,  0.0224455 , -0.0455707 ,
        0.        , -0.0023271 ,  0.01513596, -0.00957174,  0.        ,
        0.        ])
2025-08-13 21:42:07 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:07 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:42:07 - shap - INFO - phi = array([ 0.05584154,  0.02169966,  0.        ,  0.01633478,  0.00852633,
        0.08344806,  0.        ,  0.        , -0.0224455 ,  0.0455707 ,
        0.        ,  0.0023271 , -0.01513596,  0.00957174,  0.        ,
        0.        ])
2025-08-13 21:42:07 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:07 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:07 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:07 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:08 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:08 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:08 - shap - INFO - phi = array([-0.04617593, -0.02126624,  0.        ,  0.07120678,  0.01311142,
       -0.08401507,  0.        , -0.00601751, -0.00911164, -0.04594297,
       -0.01685909,  0.        , -0.02174373,  0.        ,  0.        ,
        0.        ])
2025-08-13 21:42:08 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:08 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:08 - shap - INFO - phi = array([ 0.04617593,  0.02126624,  0.        , -0.07120678, -0.01311142,
        0.08401507,  0.        ,  0.00601751,  0.00911164,  0.04594297,
        0.01685909,  0.        ,  0.02174373,  0.        ,  0.        ,
        0.        ])
2025-08-13 21:42:08 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:08 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:08 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:08 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:09 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:09 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:09 - shap - INFO - phi = array([-0.05800275, -0.02237621,  0.        ,  0.07424091,  0.01247523,
        0.02238623,  0.        ,  0.        ,  0.        , -0.04854339,
        0.        , -0.00347978,  0.0099245 ,  0.00687223, -0.00523943,
        0.        ])
2025-08-13 21:42:09 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:09 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:09 - shap - INFO - phi = array([ 0.05800275,  0.02237621,  0.        , -0.07424091, -0.01247523,
       -0.02238623,  0.        ,  0.        ,  0.        ,  0.04854339,
        0.        ,  0.00347978, -0.0099245 , -0.00687223,  0.00523943,
        0.        ])
2025-08-13 21:42:09 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:09 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:09 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:09 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:10 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:10 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:10 - shap - INFO - phi = array([0.07651029, 0.00966816, 0.        , 0.06634335, 0.        ,
       0.02016953, 0.00105542, 0.0117969 , 0.01907651, 0.02765759,
       0.        , 0.        , 0.01172201, 0.00805388, 0.        ,
       0.        ])
2025-08-13 21:42:10 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:10 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:10 - shap - INFO - phi = array([-0.07651029, -0.00966816,  0.        , -0.06634335,  0.        ,
       -0.02016953, -0.00105542, -0.0117969 , -0.01907651, -0.02765759,
        0.        ,  0.        , -0.01172201, -0.00805388,  0.        ,
        0.        ])
2025-08-13 21:42:10 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:10 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:10 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:10 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:11 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:11 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:42:11 - shap - INFO - phi = array([-0.04864557, -0.02255001,  0.        ,  0.01284388,  0.        ,
        0.02089951,  0.        , -0.0073721 , -0.01103834, -0.04890662,
       -0.01885729,  0.        ,  0.02715069, -0.00739235,  0.        ,
        0.        ])
2025-08-13 21:42:11 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:11 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:42:11 - shap - INFO - phi = array([ 0.04864557,  0.02255001,  0.        , -0.01284388,  0.        ,
       -0.02089951,  0.        ,  0.0073721 ,  0.01103834,  0.04890662,
        0.01885729,  0.        , -0.02715069,  0.00739235,  0.        ,
        0.        ])
2025-08-13 21:42:11 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:11 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:11 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:11 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:12 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:12 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:12 - shap - INFO - phi = array([ 0.04109739,  0.01228057, -0.01316391,  0.0723102 ,  0.        ,
        0.02244095,  0.        ,  0.01512748,  0.        ,  0.03093661,
        0.        ,  0.        , -0.02450716, -0.01220791, -0.00655054,
        0.        ])
2025-08-13 21:42:12 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:12 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:12 - shap - INFO - phi = array([-0.04109739, -0.01228057,  0.01316391, -0.0723102 ,  0.        ,
       -0.02244095,  0.        , -0.01512748,  0.        , -0.03093661,
        0.        ,  0.        ,  0.02450716,  0.01220791,  0.00655054,
        0.        ])
2025-08-13 21:42:12 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:12 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:12 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:12 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:13 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:13 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:13 - shap - INFO - phi = array([-0.04810204,  0.00819112,  0.        , -0.01739611,  0.        ,
        0.04293499,  0.        ,  0.        , -0.01123909,  0.03193461,
        0.        ,  0.        ,  0.02749722, -0.01141265,  0.01381775,
        0.00262336])
2025-08-13 21:42:13 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:13 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:13 - shap - INFO - phi = array([ 0.04810204, -0.00819112,  0.        ,  0.01739611,  0.        ,
       -0.04293499,  0.        ,  0.        ,  0.01123909, -0.03193461,
        0.        ,  0.        , -0.02749722,  0.01141265, -0.01381775,
       -0.00262336])
2025-08-13 21:42:13 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:13 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:13 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:13 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:14 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:14 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:14 - shap - INFO - phi = array([ 0.08228084,  0.03456029,  0.        , -0.01687636,  0.01191802,
        0.04307399,  0.        ,  0.        , -0.05865311,  0.        ,
        0.        ,  0.00609717,  0.02667116,  0.        , -0.00612024,
        0.0023605 ])
2025-08-13 21:42:14 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:14 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:14 - shap - INFO - phi = array([-0.08228084, -0.03456029,  0.        ,  0.01687636, -0.01191802,
       -0.04307399,  0.        ,  0.        ,  0.05865311,  0.        ,
        0.        , -0.00609717, -0.02667116,  0.        ,  0.00612024,
       -0.0023605 ])
2025-08-13 21:42:14 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:14 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:14 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:14 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:15 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:15 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:15 - shap - INFO - phi = array([ 0.06366536,  0.01305536,  0.        , -0.01691951, -0.00273824,
        0.04339926,  0.        ,  0.00191707,  0.        ,  0.03195499,
       -0.0036256 ,  0.        ,  0.        ,  0.        , -0.00573819,
       -0.00664666])
2025-08-13 21:42:15 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:15 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:15 - shap - INFO - phi = array([-0.06366536, -0.01305536,  0.        ,  0.01691951,  0.00273824,
       -0.04339926,  0.        , -0.00191707,  0.        , -0.03195499,
        0.0036256 ,  0.        ,  0.        ,  0.        ,  0.00573819,
        0.00664666])
2025-08-13 21:42:15 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:15 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:15 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:15 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:16 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:16 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:42:16 - shap - INFO - phi = array([ 0.04155856,  0.03526853,  0.        , -0.01724998,  0.01231619,
        0.04339832,  0.        ,  0.        ,  0.        ,  0.        ,
       -0.01920825, -0.00388042, -0.0243734 ,  0.        , -0.00580892,
        0.00286689])
2025-08-13 21:42:16 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:16 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:42:16 - shap - INFO - phi = array([-0.04155856, -0.03526853,  0.        ,  0.01724998, -0.01231619,
       -0.04339832,  0.        ,  0.        ,  0.        ,  0.        ,
        0.01920825,  0.00388042,  0.0243734 ,  0.        ,  0.00580892,
       -0.00286689])
2025-08-13 21:42:16 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:16 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:16 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:16 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:16 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:16 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:42:16 - shap - INFO - phi = array([-0.05869452,  0.00808628,  0.        , -0.01719578, -0.00981868,
        0.04282324,  0.        ,  0.        ,  0.        ,  0.03190004,
       -0.01903771,  0.        ,  0.0155609 ,  0.        , -0.00557955,
        0.00299884])
2025-08-13 21:42:16 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:16 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:42:16 - shap - INFO - phi = array([ 0.05869452, -0.00808628,  0.        ,  0.01719578,  0.00981868,
       -0.04282324,  0.        ,  0.        ,  0.        , -0.03190004,
        0.01903771,  0.        , -0.0155609 ,  0.        ,  0.00557955,
       -0.00299884])
2025-08-13 21:42:16 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:16 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:16 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:16 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:17 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:17 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:17 - shap - INFO - phi = array([ 0.06234516,  0.03377213,  0.        ,  0.01257796,  0.01130461,
        0.02228248,  0.        ,  0.        ,  0.02252161,  0.        ,
        0.02057775,  0.00562887, -0.0240051 ,  0.01102661,  0.        ,
        0.        ])
2025-08-13 21:42:17 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:17 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:17 - shap - INFO - phi = array([-0.06234516, -0.03377213,  0.        , -0.01257796, -0.01130461,
       -0.02228248,  0.        ,  0.        , -0.02252161,  0.        ,
       -0.02057775, -0.00562887,  0.0240051 , -0.01102661,  0.        ,
        0.        ])
2025-08-13 21:42:17 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:17 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:17 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:17 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:18 - shap - INFO - np.sum(w_aug) = 16.0
2025-08-13 21:42:18 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:18 - shap - INFO - phi = array([-0.05483521, -0.02088601,  0.        , -0.01568673, -0.00868086,
       -0.08046667,  0.        ,  0.        , -0.05340998, -0.0444421 ,
        0.        ,  0.        , -0.0008575 , -0.00963682,  0.01258299,
        0.        ])
2025-08-13 21:42:18 - shap - INFO - np.sum(w_aug) = 16.0
2025-08-13 21:42:18 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:18 - shap - INFO - phi = array([ 0.05483521,  0.02088601,  0.        ,  0.01568673,  0.00868086,
        0.08046667,  0.        ,  0.        ,  0.05340998,  0.0444421 ,
        0.        ,  0.        ,  0.0008575 ,  0.00963682, -0.01258299,
        0.        ])
2025-08-13 21:42:18 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:18 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:18 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:18 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:19 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:19 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:19 - shap - INFO - phi = array([ 0.0300147 , -0.02282516,  0.        , -0.01764306,  0.01224891,
        0.02321923,  0.        ,  0.        , -0.01089135,  0.03223968,
        0.02201495,  0.        , -0.02373848, -0.01136704,  0.        ,
        0.        ])
2025-08-13 21:42:19 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:19 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:19 - shap - INFO - phi = array([-0.0300147 ,  0.02282516,  0.        ,  0.01764306, -0.01224891,
       -0.02321923,  0.        ,  0.        ,  0.01089135, -0.03223968,
       -0.02201495,  0.        ,  0.02373848,  0.01136704,  0.        ,
        0.        ])
2025-08-13 21:42:19 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:19 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:19 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:19 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:20 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:20 - shap - INFO - phi = array([-0.04904163, -0.0231716 ,  0.        , -0.01741227, -0.0028066 ,
       -0.02802247,  0.        ,  0.        ,  0.        ,  0.03082594,
        0.        ,  0.        ,  0.02278623, -0.0078027 , -0.00556647,
       -0.00636919])
2025-08-13 21:42:20 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:20 - shap - INFO - phi = array([ 0.04904163,  0.0231716 ,  0.        ,  0.01741227,  0.0028066 ,
        0.02802247,  0.        ,  0.        ,  0.        , -0.03082594,
        0.        ,  0.        , -0.02278623,  0.0078027 ,  0.00556647,
        0.00636919])
2025-08-13 21:42:20 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:20 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:20 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:20 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:20 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:20 - shap - INFO - phi = array([ 0.08237004,  0.03434627,  0.        , -0.01732421,  0.        ,
        0.0222428 ,  0.        ,  0.00152817, -0.01144117,  0.        ,
        0.02103663,  0.        ,  0.        ,  0.00608941, -0.00623274,
        0.00203832])
2025-08-13 21:42:20 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:20 - shap - INFO - phi = array([-0.08237004, -0.03434627,  0.        ,  0.01732421,  0.        ,
       -0.0222428 ,  0.        , -0.00152817,  0.01144117,  0.        ,
       -0.02103663,  0.        ,  0.        , -0.00608941,  0.00623274,
       -0.00203832])
2025-08-13 21:42:20 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:20 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:20 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:20 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:21 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:21 - shap - INFO - phi = array([-0.05675162, -0.02165049,  0.        , -0.01638733, -0.00921894,
       -0.08367395,  0.        ,  0.        , -0.00095739, -0.04636544,
        0.        ,  0.        ,  0.01411763,  0.        , -0.00491265,
        0.00296103])
2025-08-13 21:42:21 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:21 - shap - INFO - phi = array([ 0.05675162,  0.02165049,  0.        ,  0.01638733,  0.00921894,
        0.08367395,  0.        ,  0.        ,  0.00095739,  0.04636544,
        0.        ,  0.        , -0.01411763,  0.        ,  0.00491265,
       -0.00296103])
2025-08-13 21:42:21 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:21 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:21 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:21 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:22 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:22 - shap - INFO - phi = array([-0.04850613, -0.0223676 ,  0.        , -0.01677899, -0.00234173,
        0.02033436,  0.        ,  0.        , -0.01057585, -0.04825571,
        0.        , -0.00319113, -0.0230866 ,  0.        ,  0.00849383,
        0.        ])
2025-08-13 21:42:22 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:22 - shap - INFO - phi = array([ 0.04850613,  0.0223676 ,  0.        ,  0.01677899,  0.00234173,
       -0.02033436,  0.        ,  0.        ,  0.01057585,  0.04825571,
        0.        ,  0.00319113,  0.0230866 ,  0.        , -0.00849383,
        0.        ])
2025-08-13 21:42:22 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:22 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:22 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:22 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:23 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:23 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:23 - shap - INFO - phi = array([ 0.04165721,  0.01239442,  0.        , -0.01686685, -0.01039861,
        0.04290603,  0.        ,  0.        ,  0.02246382,  0.0310533 ,
        0.01591545,  0.        ,  0.0259305 ,  0.0107237 ,  0.        ,
        0.        ])
2025-08-13 21:42:23 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:23 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:23 - shap - INFO - phi = array([-0.04165721, -0.01239442,  0.        ,  0.01686685,  0.01039861,
       -0.04290603,  0.        ,  0.        , -0.02246382, -0.0310533 ,
       -0.01591545,  0.        , -0.0259305 , -0.0107237 ,  0.        ,
        0.        ])
2025-08-13 21:42:23 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:23 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:23 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:23 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:24 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:24 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:24 - shap - INFO - phi = array([-0.05467971, -0.02039422,  0.        , -0.01519148, -0.00159905,
       -0.08153158,  0.        , -0.00588902, -0.00912725, -0.04492721,
        0.        ,  0.        , -0.00329692, -0.00837545,  0.        ,
        0.        ])
2025-08-13 21:42:24 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:24 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:24 - shap - INFO - phi = array([0.05467971, 0.02039422, 0.        , 0.01519148, 0.00159905,
       0.08153158, 0.        , 0.00588902, 0.00912725, 0.04492721,
       0.        , 0.        , 0.00329692, 0.00837545, 0.        ,
       0.        ])
2025-08-13 21:42:24 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:24 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:24 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:24 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:24 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:24 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:24 - shap - INFO - phi = array([-0.0535666 , -0.02079485,  0.        , -0.01556449, -0.00855161,
       -0.07901252,  0.        ,  0.        , -0.05278227, -0.04369426,
        0.        ,  0.        , -0.02058126,  0.        , -0.00460383,
        0.00254525])
2025-08-13 21:42:24 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:24 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:24 - shap - INFO - phi = array([ 0.0535666 ,  0.02079485,  0.        ,  0.01556449,  0.00855161,
        0.07901252,  0.        ,  0.        ,  0.05278227,  0.04369426,
        0.        ,  0.        ,  0.02058126,  0.        ,  0.00460383,
       -0.00254525])
2025-08-13 21:42:24 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:24 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:24 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:24 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:25 - shap - INFO - np.sum(w_aug) = 16.0
2025-08-13 21:42:25 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:25 - shap - INFO - phi = array([-0.05856283,  0.00988273,  0.        ,  0.        ,  0.00914686,
        0.04029883,  0.        ,  0.01368587,  0.02061819,  0.02922353,
        0.01832369,  0.        ,  0.02379932,  0.        ,  0.00476248,
        0.        ])
2025-08-13 21:42:25 - shap - INFO - np.sum(w_aug) = 16.0
2025-08-13 21:42:25 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:25 - shap - INFO - phi = array([ 0.05856283, -0.00988273,  0.        ,  0.        , -0.00914686,
       -0.04029883,  0.        , -0.01368587, -0.02061819, -0.02922353,
       -0.01832369,  0.        , -0.02379932,  0.        , -0.00476248,
        0.        ])
2025-08-13 21:42:25 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:25 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:25 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:25 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:26 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:26 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:26 - shap - INFO - phi = array([-0.05774441, -0.02225285,  0.        , -0.01635518, -0.00892073,
       -0.06103718,  0.        ,  0.        ,  0.        ,  0.02967858,
       -0.01774395,  0.        , -0.02232134, -0.01019129,  0.00859058,
        0.        ])
2025-08-13 21:42:26 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:26 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:26 - shap - INFO - phi = array([ 0.05774441,  0.02225285,  0.        ,  0.01635518,  0.00892073,
        0.06103718,  0.        ,  0.        ,  0.        , -0.02967858,
        0.01774395,  0.        ,  0.02232134,  0.01019129, -0.00859058,
        0.        ])
2025-08-13 21:42:26 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:26 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:26 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:26 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:27 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:27 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:27 - shap - INFO - phi = array([ 0.08203572,  0.03405371,  0.00124565, -0.01760492,  0.        ,
        0.04258684,  0.        , -0.00842837,  0.        ,  0.        ,
       -0.01947327,  0.        ,  0.        ,  0.00129222,  0.00719423,
        0.00193925])
2025-08-13 21:42:27 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:27 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:27 - shap - INFO - phi = array([-0.08203572, -0.03405371, -0.00124565,  0.01760492,  0.        ,
       -0.04258684,  0.        ,  0.00842837,  0.        ,  0.        ,
        0.01947327,  0.        ,  0.        , -0.00129222, -0.00719423,
       -0.00193925])
2025-08-13 21:42:27 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:27 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:27 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:27 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:28 - shap - INFO - np.sum(w_aug) = 15.999999999999995
2025-08-13 21:42:28 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:28 - shap - INFO - phi = array([-0.05949598,  0.00704945,  0.        , -0.01772295,  0.        ,
       -0.0631038 ,  0.        ,  0.        ,  0.02246536,  0.03017835,
       -0.01965884, -0.00417329,  0.        ,  0.00593003,  0.00762124,
        0.        ])
2025-08-13 21:42:28 - shap - INFO - np.sum(w_aug) = 15.999999999999995
2025-08-13 21:42:28 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:42:28 - shap - INFO - phi = array([ 0.05949598, -0.00704945,  0.        ,  0.01772295,  0.        ,
        0.0631038 ,  0.        ,  0.        , -0.02246536, -0.03017835,
        0.01965884,  0.00417329,  0.        , -0.00593003, -0.00762124,
        0.        ])
2025-08-13 21:42:28 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:28 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:28 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:28 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:28 - shap - INFO - np.sum(w_aug) = 16.0
2025-08-13 21:42:28 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:28 - shap - INFO - phi = array([ 0.08230061,  0.01256407,  0.        , -0.0172853 ,  0.01171814,
        0.04276576,  0.        ,  0.        ,  0.        ,  0.03124677,
        0.02082983,  0.        , -0.02401813, -0.01151093,  0.        ,
       -0.00681551])
2025-08-13 21:42:28 - shap - INFO - np.sum(w_aug) = 16.0
2025-08-13 21:42:28 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:28 - shap - INFO - phi = array([-0.08230061, -0.01256407,  0.        ,  0.0172853 , -0.01171814,
       -0.04276576,  0.        ,  0.        ,  0.        , -0.03124677,
       -0.02082983,  0.        ,  0.02401813,  0.01151093,  0.        ,
        0.00681551])
2025-08-13 21:42:28 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:28 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:28 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:29 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:29 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:29 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:42:29 - shap - INFO - phi = array([-0.05588842, -0.02174425,  0.        , -0.01601989, -0.00265047,
       -0.08242713,  0.        , -0.00688825,  0.        , -0.04567767,
        0.        , -0.00326408, -0.02177939,  0.        ,  0.00790499,
        0.        ])
2025-08-13 21:42:29 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:42:29 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:42:29 - shap - INFO - phi = array([ 0.05588842,  0.02174425,  0.        ,  0.01601989,  0.00265047,
        0.08242713,  0.        ,  0.00688825,  0.        ,  0.04567767,
        0.        ,  0.00326408,  0.02177939,  0.        , -0.00790499,
        0.        ])
2025-08-13 21:42:29 - shap - INFO - num_full_subsets = 2
2025-08-13 21:42:29 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:42:29 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:42:29 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:42:30 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:30 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:30 - shap - INFO - phi = array([ 0.08135568,  0.03384524,  0.00167018, -0.01658347,  0.        ,
        0.04273575,  0.        ,  0.        ,  0.02266138,  0.03099547,
        0.        ,  0.        ,  0.        ,  0.0061609 , -0.00588153,
       -0.00665063])
2025-08-13 21:42:30 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:42:30 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:42:30 - shap - INFO - phi = array([-0.08135568, -0.03384524, -0.00167018,  0.01658347,  0.        ,
       -0.04273575,  0.        ,  0.        , -0.02266138, -0.03099547,
        0.        ,  0.        ,  0.        , -0.0061609 ,  0.00588153,
        0.00665063])
2025-08-13 21:42:32 - enhanced_shap_visualization - INFO - Complete SHAP analysis generated for boosting
2025-08-13 21:42:32 - model_ensemble - INFO -   boosting 完整SHAP分析完成
2025-08-13 21:42:32 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-08-13 21:42:32 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-08-13 21:42:32 - model_ensemble - INFO -     importance: 1 个文件
2025-08-13 21:42:32 - model_ensemble - INFO -     force: 3 个文件
2025-08-13 21:42:32 - model_ensemble - INFO -     waterfall: 3 个文件
2025-08-13 21:42:32 - model_ensemble - INFO -     dependence: 5 个文件
2025-08-13 21:42:32 - model_ensemble - INFO -     decision: 1 个文件
2025-08-13 21:42:32 - model_ensemble - INFO -   boosting SHAP分析完成
2025-08-13 21:46:00 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-08-13 21:46:00 - safe_visualization - INFO - Ensemble performance plot saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_performance.pdf
2025-08-13 21:46:00 - safe_visualization - INFO - Summary report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\ensemble_summary_report.txt
2025-08-13 21:46:01 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:01 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:01 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:01 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:01 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:01 - shap - INFO - phi = array([ 0.        , -0.00680354,  0.        ,  0.00589126, -0.00349178,
        0.        ,  0.        ,  0.        , -0.01138221,  0.        ,
        0.000473  ,  0.        ,  0.00259893, -0.00119959,  0.        ,
        0.        ])
2025-08-13 21:46:01 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:01 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:01 - shap - INFO - phi = array([ 0.        ,  0.00680354,  0.        , -0.00589126,  0.00349178,
        0.        ,  0.        ,  0.        ,  0.01138221,  0.        ,
       -0.000473  ,  0.        , -0.00259893,  0.00119959,  0.        ,
        0.        ])
2025-08-13 21:46:01 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:01 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:01 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:01 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:02 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:02 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:02 - shap - INFO - phi = array([ 0.        , -0.00680198,  0.        ,  0.0058876 , -0.00348918,
        0.        ,  0.        ,  0.        , -0.01137809,  0.        ,
        0.00047133,  0.        ,  0.00259564, -0.00119925,  0.        ,
        0.        ])
2025-08-13 21:46:02 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:02 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:02 - shap - INFO - phi = array([ 0.        ,  0.00680198,  0.        , -0.0058876 ,  0.00348918,
        0.        ,  0.        ,  0.        ,  0.01137809,  0.        ,
       -0.00047133,  0.        , -0.00259564,  0.00119925,  0.        ,
        0.        ])
2025-08-13 21:46:02 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:02 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:02 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:02 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:03 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:03 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:03 - shap - INFO - phi = array([ 0.        ,  0.0138191 ,  0.        ,  0.00609919,  0.00529489,
        0.        ,  0.        ,  0.        , -0.0116865 ,  0.        ,
        0.00048499,  0.        ,  0.00267769,  0.00793945,  0.        ,
        0.        ])
2025-08-13 21:46:03 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:03 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:03 - shap - INFO - phi = array([ 0.        , -0.0138191 ,  0.        , -0.00609919, -0.00529489,
        0.        ,  0.        ,  0.        ,  0.0116865 ,  0.        ,
       -0.00048499,  0.        , -0.00267769, -0.00793945,  0.        ,
        0.        ])
2025-08-13 21:46:03 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:03 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:03 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:03 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:04 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:04 - shap - INFO - phi = array([ 0.        ,  0.00500151,  0.        ,  0.00595273, -0.00352051,
        0.        ,  0.        ,  0.        , -0.01147603,  0.        ,
        0.00047663,  0.        ,  0.00262157, -0.00121186,  0.        ,
        0.        ])
2025-08-13 21:46:04 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:04 - shap - INFO - phi = array([ 0.        , -0.00500151,  0.        , -0.00595273,  0.00352051,
        0.        ,  0.        ,  0.        ,  0.01147603,  0.        ,
       -0.00047663,  0.        , -0.00262157,  0.00121186,  0.        ,
        0.        ])
2025-08-13 21:46:04 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:04 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:04 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:04 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:04 - shap - INFO - np.sum(w_aug) = 16.0
2025-08-13 21:46:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:04 - shap - INFO - phi = array([ 0.        , -0.00700785,  0.        ,  0.00612566, -0.00359806,
        0.        ,  0.        ,  0.        ,  0.03451198,  0.        ,
        0.00048498,  0.        ,  0.00268837, -0.00459361,  0.        ,
        0.        ])
2025-08-13 21:46:04 - shap - INFO - np.sum(w_aug) = 16.0
2025-08-13 21:46:04 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:04 - shap - INFO - phi = array([ 0.        ,  0.00700785,  0.        , -0.00612566,  0.00359806,
        0.        ,  0.        ,  0.        , -0.03451198,  0.        ,
       -0.00048498,  0.        , -0.00268837,  0.00459361,  0.        ,
        0.        ])
2025-08-13 21:46:05 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:05 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:05 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:05 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:05 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:46:05 - shap - INFO - phi = array([ 0.        , -0.00628223,  0.        , -0.07216034, -0.0032218 ,
        0.        ,  0.        ,  0.        , -0.01053406,  0.        ,
        0.00041404,  0.        ,  0.00239035, -0.00111201,  0.        ,
        0.        ])
2025-08-13 21:46:05 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:05 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:46:05 - shap - INFO - phi = array([ 0.        ,  0.00628223,  0.        ,  0.07216034,  0.0032218 ,
        0.        ,  0.        ,  0.        ,  0.01053406,  0.        ,
       -0.00041404,  0.        , -0.00239035,  0.00111201,  0.        ,
        0.        ])
2025-08-13 21:46:05 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:05 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:05 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:05 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:06 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:06 - shap - INFO - phi = array([ 0.        ,  0.00503787,  0.        ,  0.00600478,  0.00522558,
        0.        ,  0.        ,  0.        , -0.01155224,  0.        ,
        0.00048179,  0.        ,  0.00264157, -0.00121863,  0.        ,
        0.        ])
2025-08-13 21:46:06 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:06 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:06 - shap - INFO - phi = array([ 0.        , -0.00503787,  0.        , -0.00600478, -0.00522558,
        0.        ,  0.        ,  0.        ,  0.01155224,  0.        ,
       -0.00048179,  0.        , -0.00264157,  0.00121863,  0.        ,
        0.        ])
2025-08-13 21:46:06 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:06 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:06 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:06 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:07 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:07 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:46:07 - shap - INFO - phi = array([ 0.        ,  0.00490885,  0.        ,  0.00583388, -0.00346156,
        0.        ,  0.        ,  0.        , -0.01129526,  0.        ,
        0.00046894,  0.        , -0.01849815, -0.00119099,  0.        ,
        0.        ])
2025-08-13 21:46:07 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:07 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:46:07 - shap - INFO - phi = array([ 0.        , -0.00490885,  0.        , -0.00583388,  0.00346156,
        0.        ,  0.        ,  0.        ,  0.01129526,  0.        ,
       -0.00046894,  0.        ,  0.01849815,  0.00119099,  0.        ,
        0.        ])
2025-08-13 21:46:07 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:07 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:07 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:07 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:08 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:08 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:08 - shap - INFO - phi = array([ 0.        ,  0.0137277 ,  0.        ,  0.0060506 , -0.00357049,
        0.        ,  0.        ,  0.        , -0.01161648,  0.        ,
        0.00048372,  0.        ,  0.00266287,  0.00788497,  0.        ,
        0.        ])
2025-08-13 21:46:08 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:08 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:08 - shap - INFO - phi = array([ 0.        , -0.0137277 ,  0.        , -0.0060506 ,  0.00357049,
        0.        ,  0.        ,  0.        ,  0.01161648,  0.        ,
       -0.00048372,  0.        , -0.00266287, -0.00788497,  0.        ,
        0.        ])
2025-08-13 21:46:08 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:08 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:08 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:08 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:09 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:09 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:09 - shap - INFO - phi = array([ 0.        , -0.00680195,  0.        ,  0.00588729, -0.00349267,
        0.        ,  0.        ,  0.        , -0.01137848,  0.        ,
        0.00047378,  0.        ,  0.00259691, -0.00119881,  0.        ,
        0.        ])
2025-08-13 21:46:09 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:09 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:09 - shap - INFO - phi = array([ 0.        ,  0.00680195,  0.        , -0.00588729,  0.00349267,
        0.        ,  0.        ,  0.        ,  0.01137848,  0.        ,
       -0.00047378,  0.        , -0.00259691,  0.00119881,  0.        ,
        0.        ])
2025-08-13 21:46:09 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:09 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:09 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:09 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:09 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:09 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:09 - shap - INFO - phi = array([ 0.        , -0.00679932,  0.        ,  0.00588708, -0.00349071,
        0.        ,  0.        ,  0.        , -0.01137816,  0.        ,
        0.00047016,  0.        ,  0.00259509, -0.00119807,  0.        ,
        0.        ])
2025-08-13 21:46:09 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:09 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:09 - shap - INFO - phi = array([ 0.        ,  0.00679932,  0.        , -0.00588708,  0.00349071,
        0.        ,  0.        ,  0.        ,  0.01137816,  0.        ,
       -0.00047016,  0.        , -0.00259509,  0.00119807,  0.        ,
        0.        ])
2025-08-13 21:46:09 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:09 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:09 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:09 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:10 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:10 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:46:10 - shap - INFO - phi = array([ 0.        ,  0.01372773,  0.        ,  0.00605019,  0.00525735,
        0.        ,  0.        ,  0.        , -0.0116182 ,  0.        ,
        0.00048353,  0.        ,  0.00266125, -0.00122501,  0.        ,
        0.        ])
2025-08-13 21:46:10 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:10 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:46:10 - shap - INFO - phi = array([ 0.        , -0.01372773,  0.        , -0.00605019, -0.00525735,
        0.        ,  0.        ,  0.        ,  0.0116182 ,  0.        ,
       -0.00048353,  0.        , -0.00266125,  0.00122501,  0.        ,
        0.        ])
2025-08-13 21:46:10 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:10 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:10 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:10 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:11 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:11 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:11 - shap - INFO - phi = array([ 0.        , -0.00680108,  0.        ,  0.00588802, -0.00349114,
        0.        ,  0.        ,  0.        , -0.01137829,  0.        ,
        0.00047109,  0.        ,  0.00259707, -0.0011996 ,  0.        ,
        0.        ])
2025-08-13 21:46:11 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:11 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:11 - shap - INFO - phi = array([ 0.        ,  0.00680108,  0.        , -0.00588802,  0.00349114,
        0.        ,  0.        ,  0.        ,  0.01137829,  0.        ,
       -0.00047109,  0.        , -0.00259707,  0.0011996 ,  0.        ,
        0.        ])
2025-08-13 21:46:11 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:11 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:11 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:11 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:12 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:12 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:12 - shap - INFO - phi = array([0.        , 0.00523974, 0.        , 0.00629759, 0.00541746,
       0.        , 0.        , 0.        , 0.03530766, 0.        ,
       0.00049842, 0.        , 0.00274558, 0.00813141, 0.        ,
       0.        ])
2025-08-13 21:46:12 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:12 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:12 - shap - INFO - phi = array([ 0.        , -0.00523974,  0.        , -0.00629759, -0.00541746,
        0.        ,  0.        ,  0.        , -0.03530766,  0.        ,
       -0.00049842,  0.        , -0.00274558, -0.00813141,  0.        ,
        0.        ])
2025-08-13 21:46:12 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:12 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:12 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:12 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:13 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:13 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:46:13 - shap - INFO - phi = array([ 0.        , -0.00660621,  0.        , -0.07579904, -0.00337041,
        0.        ,  0.        ,  0.        ,  0.03237549,  0.        ,
        0.00045101,  0.        ,  0.00252082,  0.00744877,  0.        ,
        0.        ])
2025-08-13 21:46:13 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:13 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:46:13 - shap - INFO - phi = array([ 0.        ,  0.00660621,  0.        ,  0.07579904,  0.00337041,
        0.        ,  0.        ,  0.        , -0.03237549,  0.        ,
       -0.00045101,  0.        , -0.00252082, -0.00744877,  0.        ,
        0.        ])
2025-08-13 21:46:13 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:13 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:13 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:13 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:14 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:14 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:46:14 - shap - INFO - phi = array([ 0.        , -0.00684698,  0.        ,  0.00593647,  0.00517272,
        0.        ,  0.        ,  0.        , -0.01145273,  0.        ,
        0.00047605,  0.        ,  0.00261699, -0.00120544,  0.        ,
        0.        ])
2025-08-13 21:46:14 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:14 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:46:14 - shap - INFO - phi = array([ 0.        ,  0.00684698,  0.        , -0.00593647, -0.00517272,
        0.        ,  0.        ,  0.        ,  0.01145273,  0.        ,
       -0.00047605,  0.        , -0.00261699,  0.00120544,  0.        ,
        0.        ])
2025-08-13 21:46:14 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:14 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:14 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:14 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:15 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:15 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:46:15 - shap - INFO - phi = array([ 0.        , -0.00680126,  0.        ,  0.00588817, -0.00349273,
        0.        ,  0.        ,  0.        , -0.0113802 ,  0.        ,
        0.00047408,  0.        ,  0.00259874, -0.00120073,  0.        ,
        0.        ])
2025-08-13 21:46:15 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:15 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:46:15 - shap - INFO - phi = array([ 0.        ,  0.00680126,  0.        , -0.00588817,  0.00349273,
        0.        ,  0.        ,  0.        ,  0.0113802 ,  0.        ,
       -0.00047408,  0.        , -0.00259874,  0.00120073,  0.        ,
        0.        ])
2025-08-13 21:46:15 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:15 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:15 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:15 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:16 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:16 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:16 - shap - INFO - phi = array([ 0.        ,  0.01398768,  0.        ,  0.00619528,  0.00535425,
        0.        ,  0.        ,  0.        ,  0.03485054,  0.        ,
       -0.01856984,  0.        ,  0.00271189, -0.00124602,  0.        ,
        0.        ])
2025-08-13 21:46:16 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:16 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:16 - shap - INFO - phi = array([ 0.        , -0.01398768,  0.        , -0.00619528, -0.00535425,
        0.        ,  0.        ,  0.        , -0.03485054,  0.        ,
        0.01856984,  0.        , -0.00271189,  0.00124602,  0.        ,
        0.        ])
2025-08-13 21:46:16 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:16 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:16 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:16 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:17 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:17 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:17 - shap - INFO - phi = array([ 0.        ,  0.00521412,  0.        ,  0.0062504 , -0.00365904,
        0.        ,  0.        ,  0.        ,  0.03510581,  0.        ,
        0.00049764,  0.        ,  0.00273544,  0.00807783,  0.        ,
        0.        ])
2025-08-13 21:46:17 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:17 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:17 - shap - INFO - phi = array([ 0.        , -0.00521412,  0.        , -0.0062504 ,  0.00365904,
        0.        ,  0.        ,  0.        , -0.03510581,  0.        ,
       -0.00049764,  0.        , -0.00273544, -0.00807783,  0.        ,
        0.        ])
2025-08-13 21:46:17 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:17 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:17 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:17 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:18 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:18 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:18 - shap - INFO - phi = array([ 0.        , -0.00680021,  0.        ,  0.00588556, -0.00348924,
        0.        ,  0.        ,  0.        , -0.01137867,  0.        ,
        0.00047089,  0.        ,  0.00259674, -0.00119899,  0.        ,
        0.        ])
2025-08-13 21:46:18 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:18 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:18 - shap - INFO - phi = array([ 0.        ,  0.00680021,  0.        , -0.00588556,  0.00348924,
        0.        ,  0.        ,  0.        ,  0.01137867,  0.        ,
       -0.00047089,  0.        , -0.00259674,  0.00119899,  0.        ,
        0.        ])
2025-08-13 21:46:18 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:18 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:18 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:18 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:19 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:19 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:19 - shap - INFO - phi = array([ 0.        , -0.00684541,  0.        ,  0.0059381 ,  0.00517405,
        0.        ,  0.        ,  0.        , -0.01145244,  0.        ,
        0.00047503,  0.        ,  0.00261564, -0.00120789,  0.        ,
        0.        ])
2025-08-13 21:46:19 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:19 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:19 - shap - INFO - phi = array([ 0.        ,  0.00684541,  0.        , -0.0059381 , -0.00517405,
        0.        ,  0.        ,  0.        ,  0.01145244,  0.        ,
       -0.00047503,  0.        , -0.00261564,  0.00120789,  0.        ,
        0.        ])
2025-08-13 21:46:19 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:19 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:19 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:19 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:20 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:46:20 - shap - INFO - phi = array([ 0.        , -0.00673147,  0.        ,  0.00581468,  0.00508071,
        0.        ,  0.        ,  0.        , -0.01126449,  0.        ,
        0.00046846,  0.        , -0.01844722, -0.00118907,  0.        ,
        0.        ])
2025-08-13 21:46:20 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:20 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:46:20 - shap - INFO - phi = array([ 0.        ,  0.00673147,  0.        , -0.00581468, -0.00508071,
        0.        ,  0.        ,  0.        ,  0.01126449,  0.        ,
       -0.00046846,  0.        ,  0.01844722,  0.00118907,  0.        ,
        0.        ])
2025-08-13 21:46:20 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:20 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:20 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:20 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:21 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:46:21 - shap - INFO - phi = array([ 0.        , -0.00701944,  0.        ,  0.00614217, -0.00360557,
        0.        ,  0.        ,  0.        ,  0.03459365,  0.        ,
        0.00048678,  0.        ,  0.00269319, -0.00123983,  0.        ,
        0.        ])
2025-08-13 21:46:21 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:21 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:46:21 - shap - INFO - phi = array([ 0.        ,  0.00701944,  0.        , -0.00614217,  0.00360557,
        0.        ,  0.        ,  0.        , -0.03459365,  0.        ,
       -0.00048678,  0.        , -0.00269319,  0.00123983,  0.        ,
        0.        ])
2025-08-13 21:46:21 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:21 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:21 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:21 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:22 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:22 - shap - INFO - phi = array([ 0.        ,  0.01372755,  0.        ,  0.00605142,  0.00525946,
        0.        ,  0.        ,  0.        , -0.01161897,  0.        ,
        0.00048119,  0.        ,  0.00266192, -0.00122572,  0.        ,
        0.        ])
2025-08-13 21:46:22 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:22 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:22 - shap - INFO - phi = array([ 0.        , -0.01372755,  0.        , -0.00605142, -0.00525946,
        0.        ,  0.        ,  0.        ,  0.01161897,  0.        ,
       -0.00048119,  0.        , -0.00266192,  0.00122572,  0.        ,
        0.        ])
2025-08-13 21:46:22 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:22 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:22 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:22 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:23 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:23 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:23 - shap - INFO - phi = array([ 0.        , -0.0066805 ,  0.        ,  0.00576153, -0.00342594,
        0.        ,  0.        ,  0.        , -0.01118906,  0.        ,
        0.00046494,  0.        , -0.018312  , -0.00118028,  0.        ,
        0.        ])
2025-08-13 21:46:23 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:23 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:23 - shap - INFO - phi = array([ 0.        ,  0.0066805 ,  0.        , -0.00576153,  0.00342594,
        0.        ,  0.        ,  0.        ,  0.01118906,  0.        ,
       -0.00046494,  0.        ,  0.018312  ,  0.00118028,  0.        ,
        0.        ])
2025-08-13 21:46:23 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:23 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:23 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:23 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:24 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:24 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:24 - shap - INFO - phi = array([ 0.        ,  0.00521021,  0.        ,  0.00625051, -0.00365427,
        0.        ,  0.        ,  0.        ,  0.03509987,  0.        ,
        0.00049301,  0.        ,  0.00273837,  0.00808451,  0.        ,
        0.        ])
2025-08-13 21:46:24 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:24 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:24 - shap - INFO - phi = array([ 0.        , -0.00521021,  0.        , -0.00625051,  0.00365427,
        0.        ,  0.        ,  0.        , -0.03509987,  0.        ,
       -0.00049301,  0.        , -0.00273837, -0.00808451,  0.        ,
        0.        ])
2025-08-13 21:46:24 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:24 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:24 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:24 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:25 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:25 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:25 - shap - INFO - phi = array([ 0.        , -0.00680054,  0.        ,  0.00588798, -0.00349238,
        0.        ,  0.        ,  0.        , -0.01137911,  0.        ,
        0.00047283,  0.        ,  0.00259769, -0.00120039,  0.        ,
        0.        ])
2025-08-13 21:46:25 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:25 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:25 - shap - INFO - phi = array([ 0.        ,  0.00680054,  0.        , -0.00588798,  0.00349238,
        0.        ,  0.        ,  0.        ,  0.01137911,  0.        ,
       -0.00047283,  0.        , -0.00259769,  0.00120039,  0.        ,
        0.        ])
2025-08-13 21:46:25 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:25 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:25 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:25 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:26 - shap - INFO - np.sum(w_aug) = 16.0
2025-08-13 21:46:26 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:26 - shap - INFO - phi = array([ 0.        , -0.00680143,  0.        ,  0.0058893 , -0.00349093,
        0.        ,  0.        ,  0.        , -0.01137973,  0.        ,
        0.00047102,  0.        ,  0.00259889, -0.00120104,  0.        ,
        0.        ])
2025-08-13 21:46:26 - shap - INFO - np.sum(w_aug) = 16.0
2025-08-13 21:46:26 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:26 - shap - INFO - phi = array([ 0.        ,  0.00680143,  0.        , -0.0058893 ,  0.00349093,
        0.        ,  0.        ,  0.        ,  0.01137973,  0.        ,
       -0.00047102,  0.        , -0.00259889,  0.00120104,  0.        ,
        0.        ])
2025-08-13 21:46:26 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:26 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:26 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:26 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:27 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:27 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:46:27 - shap - INFO - phi = array([ 0.        ,  0.01372873,  0.        ,  0.00605061,  0.00525881,
        0.        ,  0.        ,  0.        , -0.01162025,  0.        ,
        0.00048201,  0.        ,  0.00266074, -0.0012238 ,  0.        ,
        0.        ])
2025-08-13 21:46:27 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:27 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:46:27 - shap - INFO - phi = array([ 0.        , -0.01372873,  0.        , -0.00605061, -0.00525881,
        0.        ,  0.        ,  0.        ,  0.01162025,  0.        ,
       -0.00048201,  0.        , -0.00266074,  0.0012238 ,  0.        ,
        0.        ])
2025-08-13 21:46:27 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:27 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:27 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:27 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:28 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:28 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:28 - shap - INFO - phi = array([ 0.        ,  0.00500316,  0.        ,  0.00595586, -0.00352352,
        0.        ,  0.        ,  0.        , -0.01148041,  0.        ,
        0.00047474,  0.        ,  0.00262392, -0.00120974,  0.        ,
        0.        ])
2025-08-13 21:46:28 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:28 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:28 - shap - INFO - phi = array([ 0.        , -0.00500316,  0.        , -0.00595586,  0.00352352,
        0.        ,  0.        ,  0.        ,  0.01148041,  0.        ,
       -0.00047474,  0.        , -0.00262392,  0.00120974,  0.        ,
        0.        ])
2025-08-13 21:46:28 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:28 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:28 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:28 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:29 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:29 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:46:29 - shap - INFO - phi = array([ 0.        , -0.00684741,  0.        ,  0.00593859,  0.00517607,
        0.        ,  0.        ,  0.        , -0.0114554 ,  0.        ,
        0.00047574,  0.        ,  0.00261667, -0.00120717,  0.        ,
        0.        ])
2025-08-13 21:46:29 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:29 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000004
2025-08-13 21:46:29 - shap - INFO - phi = array([ 0.        ,  0.00684741,  0.        , -0.00593859, -0.00517607,
        0.        ,  0.        ,  0.        ,  0.0114554 ,  0.        ,
       -0.00047574,  0.        , -0.00261667,  0.00120717,  0.        ,
        0.        ])
2025-08-13 21:46:29 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:29 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:29 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:29 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:30 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:30 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:30 - shap - INFO - phi = array([ 0.        , -0.00678317,  0.        ,  0.00586705, -0.00348153,
        0.        ,  0.        ,  0.        , -0.01135127,  0.        ,
        0.00047175,  0.        ,  0.0025885 , -0.00443818,  0.        ,
        0.        ])
2025-08-13 21:46:30 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:30 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:30 - shap - INFO - phi = array([ 0.        ,  0.00678317,  0.        , -0.00586705,  0.00348153,
        0.        ,  0.        ,  0.        ,  0.01135127,  0.        ,
       -0.00047175,  0.        , -0.0025885 ,  0.00443818,  0.        ,
        0.        ])
2025-08-13 21:46:30 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:30 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:30 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:30 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:31 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:31 - shap - INFO - phi = array([ 0.        , -0.0068003 ,  0.        ,  0.00588842, -0.00348955,
        0.        ,  0.        ,  0.        , -0.01137944,  0.        ,
        0.00047094,  0.        ,  0.00259552, -0.00119951,  0.        ,
        0.        ])
2025-08-13 21:46:31 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:31 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:31 - shap - INFO - phi = array([ 0.        ,  0.0068003 ,  0.        , -0.00588842,  0.00348955,
        0.        ,  0.        ,  0.        ,  0.01137944,  0.        ,
       -0.00047094,  0.        , -0.00259552,  0.00119951,  0.        ,
        0.        ])
2025-08-13 21:46:31 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:31 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:31 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:31 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:32 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:46:32 - shap - INFO - phi = array([ 0.        ,  0.01372758,  0.        ,  0.00605064,  0.00525968,
        0.        ,  0.        ,  0.        , -0.01161702,  0.        ,
        0.00048366,  0.        ,  0.00265908, -0.00122677,  0.        ,
        0.        ])
2025-08-13 21:46:32 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:32 - shap - INFO - np.sum(self.kernelWeights) = 1.0
2025-08-13 21:46:32 - shap - INFO - phi = array([ 0.        , -0.01372758,  0.        , -0.00605064, -0.00525968,
        0.        ,  0.        ,  0.        ,  0.01161702,  0.        ,
       -0.00048366,  0.        , -0.00265908,  0.00122677,  0.        ,
        0.        ])
2025-08-13 21:46:32 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:32 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:32 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:32 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:33 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:33 - shap - INFO - phi = array([ 0.        , -0.00705876,  0.        ,  0.00618381,  0.00535105,
        0.        ,  0.        ,  0.        ,  0.0348147 ,  0.        ,
        0.00049222,  0.        ,  0.00271094, -0.00124799,  0.        ,
        0.        ])
2025-08-13 21:46:33 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:33 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:33 - shap - INFO - phi = array([ 0.        ,  0.00705876,  0.        , -0.00618381, -0.00535105,
        0.        ,  0.        ,  0.        , -0.0348147 ,  0.        ,
       -0.00049222,  0.        , -0.00271094,  0.00124799,  0.        ,
        0.        ])
2025-08-13 21:46:33 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:33 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:33 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:33 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:34 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:34 - shap - INFO - phi = array([ 0.        , -0.00641945,  0.        , -0.07378236, -0.00330045,
        0.        ,  0.        ,  0.        ,  0.03143561,  0.        ,
        0.00045224,  0.        , -0.01762035, -0.00112397,  0.        ,
        0.        ])
2025-08-13 21:46:34 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:34 - shap - INFO - phi = array([ 0.        ,  0.00641945,  0.        ,  0.07378236,  0.00330045,
        0.        ,  0.        ,  0.        , -0.03143561,  0.        ,
       -0.00045224,  0.        ,  0.01762035,  0.00112397,  0.        ,
        0.        ])
2025-08-13 21:46:34 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:34 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:34 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:34 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:34 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:34 - shap - INFO - phi = array([ 0.        ,  0.00503964,  0.        ,  0.00600441,  0.00522416,
        0.        ,  0.        ,  0.        , -0.01155018,  0.        ,
        0.00047909,  0.        ,  0.00264053, -0.00121692,  0.        ,
        0.        ])
2025-08-13 21:46:34 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:34 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:34 - shap - INFO - phi = array([ 0.        , -0.00503964,  0.        , -0.00600441, -0.00522416,
        0.        ,  0.        ,  0.        ,  0.01155018,  0.        ,
       -0.00047909,  0.        , -0.00264053,  0.00121692,  0.        ,
        0.        ])
2025-08-13 21:46:34 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:34 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:34 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:34 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:35 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:35 - shap - INFO - phi = array([ 0.        ,  0.0138714 ,  0.        ,  0.0061308 , -0.00359976,
        0.        ,  0.        ,  0.        ,  0.03455585,  0.        ,
        0.00049028,  0.        , -0.01924929, -0.00123694,  0.        ,
        0.        ])
2025-08-13 21:46:35 - shap - INFO - np.sum(w_aug) = 15.999999999999998
2025-08-13 21:46:35 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:35 - shap - INFO - phi = array([ 0.        , -0.0138714 ,  0.        , -0.0061308 ,  0.00359976,
        0.        ,  0.        ,  0.        , -0.03455585,  0.        ,
       -0.00049028,  0.        ,  0.01924929,  0.00123694,  0.        ,
        0.        ])
2025-08-13 21:46:35 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:35 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:35 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:35 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:36 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:36 - shap - INFO - phi = array([ 0.        , -0.00684583,  0.        ,  0.0059377 ,  0.00517424,
        0.        ,  0.        ,  0.        , -0.01145217,  0.        ,
        0.00047356,  0.        ,  0.002616  , -0.0012064 ,  0.        ,
        0.        ])
2025-08-13 21:46:36 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:36 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:36 - shap - INFO - phi = array([ 0.        ,  0.00684583,  0.        , -0.0059377 , -0.00517424,
        0.        ,  0.        ,  0.        ,  0.01145217,  0.        ,
       -0.00047356,  0.        , -0.002616  ,  0.0012064 ,  0.        ,
        0.        ])
2025-08-13 21:46:36 - shap - INFO - num_full_subsets = 2
2025-08-13 21:46:36 - shap - INFO - remaining_weight_vector = array([0.22727203, 0.18465852, 0.16115653, 0.14772682, 0.14069221,
       0.13849389])
2025-08-13 21:46:36 - shap - INFO - num_paired_subset_sizes = 7
2025-08-13 21:46:36 - shap - INFO - weight_left = 0.5063344810024111
2025-08-13 21:46:37 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:37 - shap - INFO - phi = array([ 0.        ,  0.00503976,  0.        ,  0.00600354,  0.00522243,
        0.        ,  0.        ,  0.        , -0.01155202,  0.        ,
        0.00048097,  0.        ,  0.00264158, -0.00121554,  0.        ,
        0.        ])
2025-08-13 21:46:37 - shap - INFO - np.sum(w_aug) = 15.999999999999996
2025-08-13 21:46:37 - shap - INFO - np.sum(self.kernelWeights) = 1.0000000000000002
2025-08-13 21:46:37 - shap - INFO - phi = array([ 0.        , -0.00503976,  0.        , -0.00600354, -0.00522243,
        0.        ,  0.        ,  0.        ,  0.01155202,  0.        ,
       -0.00048097,  0.        , -0.00264158,  0.00121554,  0.        ,
        0.        ])
