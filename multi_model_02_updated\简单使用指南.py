#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推理功能简单使用指南
最实用的推理功能使用方法
"""

import sys
import numpy as np
import pandas as pd
from pathlib import Path

# 添加代码路径
sys.path.append(str(Path(__file__).parent / 'code'))

def example_1_single_prediction():
    """示例1: 单样本预测 - 最常用"""
    print("=" * 50)
    print("示例1: 单样本预测")
    print("=" * 50)
    
    from prediction_api import predict
    
    # 准备单个样本数据（16个特征，根据您的模型训练数据）
    sample = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 
              9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0]
    
    # 使用最好的几个模型进行预测
    result = predict(sample, models=['RandomForest', 'XGBoost', 'LightGBM'])
    
    if result.get('success'):
        print("预测成功！")
        predictions = result['predictions']
        for model, prob in predictions.items():
            print(f"{model}: {prob:.4f} (概率)")
            
        # 简单的决策建议
        avg_prob = sum(predictions.values()) / len(predictions)
        decision = "正类" if avg_prob > 0.5 else "负类"
        confidence = max(avg_prob, 1-avg_prob)
        print(f"\n建议决策: {decision} (置信度: {confidence:.2%})")
    else:
        print(f"预测失败: {result.get('error')}")


def example_2_csv_file_prediction():
    """示例2: CSV文件预测 - 最实用"""
    print("\n" + "=" * 50)
    print("示例2: CSV文件预测")
    print("=" * 50)
    
    from prediction_api import predict_file
    
    # 创建示例CSV文件
    print("创建示例CSV文件...")
    data = pd.DataFrame(
        np.random.rand(10, 16),
        columns=[f'feature_{i}' for i in range(16)]
    )
    
    csv_file = "my_data.csv"
    data.to_csv(csv_file, index=False)
    print(f"CSV文件已创建: {csv_file}")
    
    # 预测CSV文件
    print("\n开始预测...")
    result = predict_file(
        csv_file,
        models=['RandomForest', 'XGBoost'],  # 选择最好的模型
        save_results=True
    )
    
    if result.get('success'):
        print("文件预测成功！")
        print(f"预测了 {result.get('sample_count')} 个样本")
        print(f"使用模型: {result.get('models_used')}")
        print(f"结果文件: {result.get('output_file', '未保存')}")
        
        # 显示预测结果摘要
        predictions = result.get('predictions', {})
        for model, preds in predictions.items():
            avg_pred = np.mean(preds)
            print(f"{model} 平均预测值: {avg_pred:.4f}")
    else:
        print(f"预测失败: {result.get('error')}")
    
    # 清理文件
    try:
        Path(csv_file).unlink()
        print(f"示例文件已清理")
    except:
        pass


def example_3_your_own_data():
    """示例3: 使用您自己的数据"""
    print("\n" + "=" * 50)
    print("示例3: 使用您自己的数据")
    print("=" * 50)
    
    print("使用您自己的数据进行预测:")
    print()
    print("方法1 - 单样本预测:")
    print("```python")
    print("from prediction_api import predict")
    print()
    print("# 您的数据（16个特征值）")
    print("my_sample = [特征1, 特征2, ..., 特征16]")
    print()
    print("# 预测")
    print("result = predict(my_sample)")
    print("if result.get('success'):")
    print("    for model, prob in result['predictions'].items():")
    print("        print(f'{model}: {prob:.4f}')")
    print("```")
    print()
    print("方法2 - CSV文件预测:")
    print("```python")
    print("from prediction_api import predict_file")
    print()
    print("# 预测您的CSV文件")
    print("result = predict_file('您的数据.csv', save_results=True)")
    print("print(f'结果保存至: {result.get(\"output_file\")}')")
    print("```")
    print()
    print("方法3 - 大文件批量预测:")
    print("```python")
    print("from batch_prediction import batch_predict_file")
    print()
    print("# 处理大型CSV文件")
    print("result = batch_predict_file('大文件.csv', chunk_size=1000)")
    print("print(f'处理了 {result.get(\"processed_samples\")} 个样本')")
    print("```")


def example_4_model_comparison():
    """示例4: 模型比较"""
    print("\n" + "=" * 50)
    print("示例4: 模型比较")
    print("=" * 50)
    
    from prediction_api import PredictionAPI
    
    # 创建API实例
    api = PredictionAPI()
    
    # 创建测试数据
    test_data = np.random.rand(20, 16)
    
    # 比较不同模型的预测结果
    comparison = api.compare_models(
        test_data,
        models=['RandomForest', 'XGBoost', 'LightGBM', 'CatBoost']
    )
    
    if comparison.get('success'):
        print("模型比较结果:")
        stats = comparison.get('comparison', {}).get('statistics', {})
        
        # 按平均预测值排序
        sorted_models = sorted(stats.items(), key=lambda x: x[1]['mean'], reverse=True)
        
        for i, (model, stat) in enumerate(sorted_models, 1):
            print(f"{i}. {model}:")
            print(f"   平均预测: {stat['mean']:.4f}")
            print(f"   稳定性: {stat['std']:.4f} (越小越稳定)")
        
        print("\n建议:")
        best_model = sorted_models[0][0]
        print(f"- 预测值最高的模型: {best_model}")
        
        # 找最稳定的模型
        most_stable = min(stats.items(), key=lambda x: x[1]['std'])
        print(f"- 最稳定的模型: {most_stable[0]} (标准差: {most_stable[1]['std']:.4f})")


def main():
    """主函数"""
    print("推理功能简单使用指南")
    print("适合日常使用的推理功能演示")
    
    try:
        example_1_single_prediction()
        example_2_csv_file_prediction()
        example_3_your_own_data()
        example_4_model_comparison()
        
        print("\n" + "=" * 50)
        print("使用总结")
        print("=" * 50)
        print("1. 单样本预测: predict(样本数据)")
        print("2. 文件预测: predict_file('文件.csv')")
        print("3. 大文件处理: batch_predict_file('大文件.csv')")
        print("4. 模型比较: api.compare_models(数据)")
        print()
        print("注意事项:")
        print("- 确保输入数据有16个特征（与训练数据一致）")
        print("- 预测结果是概率值（0-1之间）")
        print("- 大于0.5通常判断为正类，小于0.5为负类")
        print("- 可以选择特定模型或使用所有模型")
        
    except Exception as e:
        print(f"演示出错: {e}")
        print("请确保模型已训练完成")


if __name__ == "__main__":
    main()
