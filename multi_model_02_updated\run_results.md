 MM01U  & D:/anaconda/envs/multi_model/python.exe d:/Code/MM01U/multi_model_02_updated/test_gui_main.py                        
2025-08-29 08:56:15 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-29 08:56:15 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-29 08:56:15 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 08:56:15 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
SHAP库已成功导入，可解释性分析功能已启用
[OK] Safe font mode enabled (English-only)
[OK] Safe font mode enabled (English-only)
test_debounce_mechanism (__main__.TestMLPlatformGUI)
测试防抖机制 ... 2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.001秒
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.001秒
ERROR
test_error_handling_improvement (__main__.TestMLPlatformGUI)
测试错误处理改进 ... 2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
ERROR
test_get_selected_models (__main__.TestMLPlatformGUI)
测试获取选中模型功能 ... 2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
ERROR
test_gui_initialization (__main__.TestMLPlatformGUI)
测试GUI初始化 ... 2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
ERROR
test_input_validation (__main__.TestMLPlatformGUI)
测试输入验证 ... 2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
ERROR
test_log_message_functionality (__main__.TestMLPlatformGUI)
测试日志消息功能 ... 2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
ERROR
test_log_performance_functionality (__main__.TestMLPlatformGUI)
测试性能日志功能 ... 2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
ERROR
test_model_variable_initialization (__main__.TestMLPlatformGUI)
测试模型变量初始化 ... 2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
ERROR
test_thread_safe_gui_update (__main__.TestMLPlatformGUI)
测试线程安全GUI更新 ... 2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
ERROR
test_timer_manager_functionality (__main__.TestMLPlatformGUI)
测试定时器管理器功能 ... 2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 开始初始化GUI界面...
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
2025-08-29 08:56:15 - GUI - INFO - 性能统计 - 会话管理器初始化: 0.000秒
ERROR
test_cancel_all (__main__.TestTimerManager)
测试取消所有定时器 ... ok
test_cancel_nonexistent_timer (__main__.TestTimerManager)
测试取消不存在的定时器 ... ok
test_cancel_timer (__main__.TestTimerManager)
测试取消定时器 ... ok
test_initialization (__main__.TestTimerManager)
测试初始化 ... ok
test_set_timer (__main__.TestTimerManager)
测试设置定时器 ... ok
test_set_timer_no_root (__main__.TestTimerManager)
测试无root窗口时设置定时器 ... ok
test_set_timer_replace_existing (__main__.TestTimerManager)
测试替换已存在的定时器 ... ok

======================================================================
ERROR: test_debounce_mechanism (__main__.TestMLPlatformGUI)
测试防抖机制
----------------------------------------------------------------------
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 365, in test_debounce_mechanism
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'

======================================================================
ERROR: test_error_handling_improvement (__main__.TestMLPlatformGUI)
测试错误处理改进
----------------------------------------------------------------------
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 429, in test_error_handling_improvement
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'

======================================================================
ERROR: test_get_selected_models (__main__.TestMLPlatformGUI)
测试获取选中模型功能
----------------------------------------------------------------------
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 330, in test_get_selected_models
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'

======================================================================
ERROR: test_gui_initialization (__main__.TestMLPlatformGUI)
测试GUI初始化
----------------------------------------------------------------------
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 166, in test_gui_initialization
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'

======================================================================
ERROR: test_input_validation (__main__.TestMLPlatformGUI)
测试输入验证
----------------------------------------------------------------------
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 468, in test_input_validation
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'

======================================================================
ERROR: test_log_message_functionality (__main__.TestMLPlatformGUI)
测试日志消息功能
----------------------------------------------------------------------
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 263, in test_log_message_functionality
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'

======================================================================
ERROR: test_log_performance_functionality (__main__.TestMLPlatformGUI)
测试性能日志功能
----------------------------------------------------------------------
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 297, in test_log_performance_functionality
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'

======================================================================
ERROR: test_model_variable_initialization (__main__.TestMLPlatformGUI)
测试模型变量初始化
----------------------------------------------------------------------
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 194, in test_model_variable_initialization
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'

======================================================================
ERROR: test_thread_safe_gui_update (__main__.TestMLPlatformGUI)
测试线程安全GUI更新
----------------------------------------------------------------------
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 398, in test_thread_safe_gui_update
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'

======================================================================
ERROR: test_timer_manager_functionality (__main__.TestMLPlatformGUI)
测试定时器管理器功能
----------------------------------------------------------------------
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 223, in test_timer_manager_functionality
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'

----------------------------------------------------------------------
Ran 17 tests in 0.184s

FAILED (errors=10)

❌ 测试失败: 0 个失败, 10 个错误

test_debounce_mechanism (__main__.TestMLPlatformGUI):
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 365, in test_debounce_mechanism
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'


test_error_handling_improvement (__main__.TestMLPlatformGUI):
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 429, in test_error_handling_improvement
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'


test_get_selected_models (__main__.TestMLPlatformGUI):
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 330, in test_get_selected_models
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'


test_gui_initialization (__main__.TestMLPlatformGUI):
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 166, in test_gui_initialization
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'


test_input_validation (__main__.TestMLPlatformGUI):
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 468, in test_input_validation
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'


test_log_message_functionality (__main__.TestMLPlatformGUI):
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 263, in test_log_message_functionality
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'


test_log_performance_functionality (__main__.TestMLPlatformGUI):
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 297, in test_log_performance_functionality
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'


test_model_variable_initialization (__main__.TestMLPlatformGUI):
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 194, in test_model_variable_initialization
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'


test_thread_safe_gui_update (__main__.TestMLPlatformGUI):
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 398, in test_thread_safe_gui_update
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'


test_timer_manager_functionality (__main__.TestMLPlatformGUI):
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\unittest\mock.py", line 1336, in patched
    return func(*newargs, **newkeywargs)
  File "d:\Code\MM01U\multi_model_02_updated\test_gui_main.py", line 223, in test_timer_manager_functionality
    gui = MLPlatformGUI()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 186, in __init__
    self.create_menu()
  File "d:\Code\MM01U\multi_model_02_updated\gui_main.py", line 213, in create_menu
    menubar = tk.Menu(self.root)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 3277, in __init__
    Widget.__init__(self, master, 'menu', cnf, kw)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2566, in __init__
    BaseWidget._setup(self, master, cnf)
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 2544, in _setup
    count = master._last_child_ids.get(name, 0) + 1
TypeError: unsupported operand type(s) for +: 'Mock' and 'int'