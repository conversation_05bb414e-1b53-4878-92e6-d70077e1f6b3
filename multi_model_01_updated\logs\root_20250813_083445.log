2025-08-13 08:34:45 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-13 08:34:45 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-13 08:34:45 - __main__ - INFO - ============================================================
2025-08-13 08:34:45 - __main__ - INFO - 测试GUI导出功能核心逻辑
2025-08-13 08:34:45 - __main__ - INFO - ============================================================
2025-08-13 08:34:45 - __main__ - INFO - 训练集大小: (140, 6)
2025-08-13 08:34:45 - __main__ - INFO - 测试集大小: (60, 6)
2025-08-13 08:34:45 - __main__ - INFO - 开始运行集成学习...
2025-08-13 08:34:45 - model_ensemble - INFO - ============================================================
2025-08-13 08:34:45 - model_ensemble - INFO - 开始运行集成学习管道
2025-08-13 08:34:45 - model_ensemble - INFO - ============================================================
2025-08-13 08:34:45 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-08-13 08:34:45 - model_ensemble - INFO - 集成方法: ['voting']
2025-08-13 08:34:45 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-08-13 08:34:45 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-08-13 08:34:45 - model_training - INFO - 模型名称: Random Forest
2025-08-13 08:34:46 - model_training - INFO - 准确率: 0.9500
2025-08-13 08:34:46 - model_training - INFO - AUC: 0.9933
2025-08-13 08:34:46 - model_training - INFO - AUPRC: 0.9944
2025-08-13 08:34:46 - model_training - INFO - 混淆矩阵:
2025-08-13 08:34:46 - model_training - INFO - 
[[28  2]
 [ 1 29]]
2025-08-13 08:34:46 - model_training - INFO - 
分类报告:
2025-08-13 08:34:46 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.97      0.93      0.95        30
           1       0.94      0.97      0.95        30

    accuracy                           0.95        60
   macro avg       0.95      0.95      0.95        60
weighted avg       0.95      0.95      0.95        60

2025-08-13 08:34:46 - model_training - INFO - 训练时间: 0.11 秒
2025-08-13 08:34:46 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9500
2025-08-13 08:34:46 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-13 08:34:46 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-13 08:34:46 - model_ensemble - INFO -   RandomForest 训练完成
2025-08-13 08:34:46 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-08-13 08:34:46 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.000 以处理不平衡
2025-08-13 08:34:46 - model_training - INFO - 模型名称: XGBoost
2025-08-13 08:34:46 - model_training - INFO - 准确率: 0.9500
2025-08-13 08:34:46 - model_training - INFO - AUC: 0.9889
2025-08-13 08:34:46 - model_training - INFO - AUPRC: 0.9902
2025-08-13 08:34:46 - model_training - INFO - 混淆矩阵:
2025-08-13 08:34:46 - model_training - INFO - 
[[29  1]
 [ 2 28]]
2025-08-13 08:34:46 - model_training - INFO - 
分类报告:
2025-08-13 08:34:46 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.97      0.95        30
           1       0.97      0.93      0.95        30

    accuracy                           0.95        60
   macro avg       0.95      0.95      0.95        60
weighted avg       0.95      0.95      0.95        60

2025-08-13 08:34:46 - model_training - INFO - 训练时间: 0.12 秒
2025-08-13 08:34:46 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9500
2025-08-13 08:34:46 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-13 08:34:46 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-13 08:34:46 - model_ensemble - INFO -   XGBoost 训练完成
2025-08-13 08:34:46 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-08-13 08:34:46 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-08-13 08:34:46 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-08-13 08:34:46 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 08:34:46 - model_ensemble - INFO - 集成模型 voting_soft 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_soft_results.joblib
2025-08-13 08:34:46 - model_ensemble - INFO - 集成模型 voting_soft 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_soft_feature_names.joblib
2025-08-13 08:34:46 - model_ensemble - INFO -     voting_soft - 准确率: 0.9667, F1: 0.9667
2025-08-13 08:34:46 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-08-13 08:34:46 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 08:34:46 - model_ensemble - INFO - 集成模型 voting_hard 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_hard_results.joblib
2025-08-13 08:34:46 - model_ensemble - INFO - 集成模型 voting_hard 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_hard_feature_names.joblib
2025-08-13 08:34:46 - model_ensemble - INFO -     voting_hard - 准确率: 0.9500, F1: 0.9500
2025-08-13 08:34:46 - model_ensemble - INFO - ============================================================
2025-08-13 08:34:46 - model_ensemble - INFO - 集成学习结果总结
2025-08-13 08:34:46 - model_ensemble - INFO - ============================================================
2025-08-13 08:34:46 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-08-13 08:34:46 - model_ensemble - INFO - 最佳F1分数: 0.9667
2025-08-13 08:34:46 - model_ensemble - INFO - 最佳准确率: 0.9667
2025-08-13 08:34:46 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-08-13 08:34:46 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9667, 精确率: 0.9667, 召回率: 0.9667, F1: 0.9667, AUC: 0.9911
2025-08-13 08:34:46 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9500, 精确率: 0.9505, 召回率: 0.9500, F1: 0.9500, AUC: 0.0000
2025-08-13 08:34:46 - __main__ - INFO - ✅ 集成学习成功！
2025-08-13 08:34:46 - __main__ - INFO - 
测试导出逻辑:
2025-08-13 08:34:46 - __main__ - INFO - ✅ CSV导出成功: test_ensemble_results_20250813_083446.csv
2025-08-13 08:34:46 - __main__ - INFO -   CSV包含 2 行数据
2025-08-13 08:34:46 - __main__ - INFO -   列名: ['模型名称', '集成方法', '准确率', '精确率', '召回率', 'F1分数', 'AUC', 'AUPRC', '平衡准确率', 'Brier分数', 'MCC', '投票类型']
2025-08-13 08:34:46 - __main__ - INFO - ✅ JSON导出成功: test_ensemble_results_20250813_083446.json
2025-08-13 08:34:46 - __main__ - INFO -   JSON包含 2 个模型结果
2025-08-13 08:34:46 - __main__ - INFO - 
导出数据示例:
2025-08-13 08:34:46 - __main__ - INFO -   模型 1: voting_soft
2025-08-13 08:34:46 - __main__ - INFO -     准确率: 0.9667
2025-08-13 08:34:46 - __main__ - INFO -     F1分数: 0.9667
2025-08-13 08:34:46 - __main__ - INFO -     AUPRC: 0.9926
2025-08-13 08:34:46 - __main__ - INFO -     平衡准确率: 0.9667
2025-08-13 08:34:46 - __main__ - INFO -     Brier分数: 0.041539550610621104
2025-08-13 08:34:46 - __main__ - INFO -   模型 2: voting_hard
2025-08-13 08:34:46 - __main__ - INFO -     准确率: 0.9500
2025-08-13 08:34:46 - __main__ - INFO -     F1分数: 0.9500
2025-08-13 08:34:46 - __main__ - INFO -     AUPRC: 0.0000
2025-08-13 08:34:46 - __main__ - INFO -     平衡准确率: 0.9500
2025-08-13 08:34:46 - __main__ - INFO -     Brier分数: None
2025-08-13 08:34:46 - __main__ - INFO - 
✅ 测试文件已清理
2025-08-13 08:34:46 - __main__ - INFO - 
测试文件夹路径逻辑:
2025-08-13 08:34:46 - __main__ - INFO - ✅ 结果目录路径: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations
2025-08-13 08:34:46 - __main__ - INFO - ✅ 目录存在: True
2025-08-13 08:34:46 - __main__ - INFO - ✅ 测试文件创建成功: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\test_file.txt
2025-08-13 08:34:46 - __main__ - INFO - ✅ 测试文件已清理
