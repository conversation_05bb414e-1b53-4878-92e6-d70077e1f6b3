@echo off
REM 多模型二分类项目 Windows 启动脚本
REM 自动检测Python环境并启动项目

echo ========================================
echo    多模型二分类机器学习平台
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请确保已安装Python并添加到PATH
    pause
    exit /b 1
)

REM 检查是否在虚拟环境中
if not defined VIRTUAL_ENV (
    echo 提示: 建议在虚拟环境中运行此项目
    echo.
)

REM 检查项目目录
if not exist "code" (
    echo 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 询问启动模式
:ask_mode
echo 请选择启动模式:
echo 1. GUI模式（推荐）
echo 2. 命令行模式
echo 3. 运行项目测试
echo 4. 退出
echo.
set /p mode_choice="请输入选项 (1-4): "

if "%mode_choice%"=="1" goto start_gui
if "%mode_choice%"=="2" goto start_cli
if "%mode_choice%"=="3" goto start_test
if "%mode_choice%"=="4" goto end
echo 无效选项，请重新选择
goto ask_mode

:start_gui
echo.
echo 启动GUI模式...
echo.
python run.py
goto end

:start_cli
echo.
echo 启动命令行模式...
echo.
python run.py --cli %*
goto end

:start_test
echo.
echo 运行项目测试...
echo.
python run.py --test
pause
goto end

:end
echo.
echo 程序已退出
pause