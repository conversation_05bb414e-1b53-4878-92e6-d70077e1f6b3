#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型服务化接口
提供HTTP API服务，支持模型的在线预测
"""

import os
import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import traceback
import warnings
warnings.filterwarnings('ignore')

try:
    from flask import Flask, request, jsonify, render_template_string
    from flask_cors import CORS
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    print("Flask未安装，无法启动HTTP服务。请运行: pip install flask flask-cors")

from prediction_api import PredictionAPI
from model_inference import ModelInference
from logger import get_logger

logger = get_logger("model_server")


class ModelServer:
    """
    模型服务器类
    提供HTTP API接口进行模型预测
    """
    
    def __init__(self, host: str = "127.0.0.1", port: int = 5000, debug: bool = False):
        """
        初始化模型服务器
        
        Args:
            host: 服务器主机地址
            port: 服务器端口
            debug: 是否开启调试模式
        """
        if not FLASK_AVAILABLE:
            raise ImportError("Flask未安装，无法创建模型服务器")
            
        self.host = host
        self.port = port
        self.debug = debug
        
        # 初始化Flask应用
        self.app = Flask(__name__)
        CORS(self.app)  # 启用跨域支持
        
        # 初始化预测API
        self.prediction_api = PredictionAPI()
        
        # 服务器统计信息
        self.stats = {
            "start_time": datetime.now().isoformat(),
            "total_requests": 0,
            "successful_predictions": 0,
            "failed_predictions": 0,
            "models_loaded": len(self.prediction_api.get_available_models())
        }
        
        # 注册路由
        self._register_routes()
        
        logger.info(f"模型服务器初始化完成，地址: {host}:{port}")
    
    def _register_routes(self):
        """注册API路由"""
        
        @self.app.route('/', methods=['GET'])
        def home():
            """主页"""
            return render_template_string(self._get_home_template())
        
        @self.app.route('/health', methods=['GET'])
        def health_check():
            """健康检查"""
            return jsonify({
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "models_loaded": len(self.prediction_api.get_available_models())
            })
        
        @self.app.route('/models', methods=['GET'])
        def get_models():
            """获取可用模型列表"""
            try:
                models = self.prediction_api.get_available_models()
                model_info = {}
                
                for model_name in models:
                    info = self.prediction_api.get_model_performance(model_name)
                    model_info[model_name] = info
                
                return jsonify({
                    "success": True,
                    "models": models,
                    "model_info": model_info,
                    "total_models": len(models)
                })
                
            except Exception as e:
                logger.error(f"获取模型列表失败: {e}")
                return jsonify({
                    "success": False,
                    "error": str(e)
                }), 500
        
        @self.app.route('/predict', methods=['POST'])
        def predict():
            """单样本预测"""
            self.stats["total_requests"] += 1
            
            try:
                data = request.get_json()
                
                if not data:
                    return jsonify({
                        "success": False,
                        "error": "请提供JSON格式的数据"
                    }), 400
                
                # 提取参数
                sample = data.get('sample')
                models = data.get('models', None)
                return_proba = data.get('return_proba', True)
                
                if sample is None:
                    return jsonify({
                        "success": False,
                        "error": "缺少必需的参数: sample"
                    }), 400
                
                # 进行预测
                result = self.prediction_api.predict_single_sample(
                    sample, models, return_proba
                )
                
                if result.get("success"):
                    self.stats["successful_predictions"] += 1
                else:
                    self.stats["failed_predictions"] += 1
                
                return jsonify(result)
                
            except Exception as e:
                self.stats["failed_predictions"] += 1
                logger.error(f"预测请求失败: {e}")
                return jsonify({
                    "success": False,
                    "error": str(e),
                    "traceback": traceback.format_exc() if self.debug else None
                }), 500
        
        @self.app.route('/predict_batch', methods=['POST'])
        def predict_batch():
            """批量预测"""
            self.stats["total_requests"] += 1
            
            try:
                data = request.get_json()
                
                if not data:
                    return jsonify({
                        "success": False,
                        "error": "请提供JSON格式的数据"
                    }), 400
                
                # 提取参数
                samples = data.get('samples')
                models = data.get('models', None)
                return_proba = data.get('return_proba', True)
                ensemble_method = data.get('ensemble_method', None)
                
                if samples is None:
                    return jsonify({
                        "success": False,
                        "error": "缺少必需的参数: samples"
                    }), 400
                
                # 转换为numpy数组
                X = np.array(samples)
                
                # 进行预测
                result = self.prediction_api.predict_from_data(
                    X, models, return_proba, ensemble_method
                )
                
                if result.get("success"):
                    self.stats["successful_predictions"] += 1
                    # 转换numpy数组为列表以便JSON序列化
                    if "predictions" in result:
                        result["predictions"] = {
                            k: v.tolist() if isinstance(v, np.ndarray) else v
                            for k, v in result["predictions"].items()
                        }
                    if "ensemble_prediction" in result:
                        ensemble_pred = result["ensemble_prediction"]
                        if isinstance(ensemble_pred, np.ndarray):
                            result["ensemble_prediction"] = ensemble_pred.tolist()
                else:
                    self.stats["failed_predictions"] += 1
                
                return jsonify(result)
                
            except Exception as e:
                self.stats["failed_predictions"] += 1
                logger.error(f"批量预测请求失败: {e}")
                return jsonify({
                    "success": False,
                    "error": str(e),
                    "traceback": traceback.format_exc() if self.debug else None
                }), 500
        
        @self.app.route('/compare', methods=['POST'])
        def compare_models():
            """模型比较"""
            self.stats["total_requests"] += 1
            
            try:
                data = request.get_json()
                
                if not data:
                    return jsonify({
                        "success": False,
                        "error": "请提供JSON格式的数据"
                    }), 400
                
                samples = data.get('samples')
                models = data.get('models', None)
                
                if samples is None:
                    return jsonify({
                        "success": False,
                        "error": "缺少必需的参数: samples"
                    }), 400
                
                # 转换为numpy数组
                X = np.array(samples)
                
                # 进行模型比较
                result = self.prediction_api.compare_models(X, models)
                
                if result.get("success"):
                    self.stats["successful_predictions"] += 1
                    # 转换numpy数组为列表
                    if "predictions" in result:
                        result["predictions"] = {
                            k: v.tolist() if isinstance(v, np.ndarray) else v
                            for k, v in result["predictions"].items()
                        }
                else:
                    self.stats["failed_predictions"] += 1
                
                return jsonify(result)
                
            except Exception as e:
                self.stats["failed_predictions"] += 1
                logger.error(f"模型比较请求失败: {e}")
                return jsonify({
                    "success": False,
                    "error": str(e),
                    "traceback": traceback.format_exc() if self.debug else None
                }), 500
        
        @self.app.route('/stats', methods=['GET'])
        def get_stats():
            """获取服务器统计信息"""
            current_stats = self.stats.copy()
            current_stats["current_time"] = datetime.now().isoformat()
            current_stats["uptime_seconds"] = (
                datetime.now() - datetime.fromisoformat(self.stats["start_time"])
            ).total_seconds()
            
            return jsonify(current_stats)
        
        @self.app.errorhandler(404)
        def not_found(error):
            return jsonify({
                "success": False,
                "error": "API端点不存在",
                "available_endpoints": [
                    "/", "/health", "/models", "/predict", 
                    "/predict_batch", "/compare", "/stats"
                ]
            }), 404
        
        @self.app.errorhandler(500)
        def internal_error(error):
            return jsonify({
                "success": False,
                "error": "服务器内部错误"
            }), 500
    
    def _get_home_template(self) -> str:
        """获取主页HTML模板"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>机器学习模型服务器</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
                .container { max-width: 800px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .header { text-align: center; color: #333; margin-bottom: 30px; }
                .api-section { margin: 20px 0; padding: 20px; background-color: #f8f9fa; border-radius: 5px; }
                .endpoint { background-color: #e9ecef; padding: 10px; margin: 10px 0; border-radius: 3px; font-family: monospace; }
                .method { color: #007bff; font-weight: bold; }
                .description { color: #666; margin-top: 5px; }
                .stats { display: flex; justify-content: space-around; margin: 20px 0; }
                .stat-box { text-align: center; padding: 15px; background-color: #e3f2fd; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🤖 机器学习模型服务器</h1>
                    <p>提供HTTP API接口进行模型预测</p>
                </div>
                
                <div class="stats">
                    <div class="stat-box">
                        <h3>{{ models_count }}</h3>
                        <p>已加载模型</p>
                    </div>
                    <div class="stat-box">
                        <h3>{{ total_requests }}</h3>
                        <p>总请求数</p>
                    </div>
                    <div class="stat-box">
                        <h3>{{ success_rate }}%</h3>
                        <p>成功率</p>
                    </div>
                </div>
                
                <div class="api-section">
                    <h2>📋 可用API端点</h2>
                    
                    <div class="endpoint">
                        <span class="method">GET</span> /health
                        <div class="description">健康检查</div>
                    </div>
                    
                    <div class="endpoint">
                        <span class="method">GET</span> /models
                        <div class="description">获取可用模型列表</div>
                    </div>
                    
                    <div class="endpoint">
                        <span class="method">POST</span> /predict
                        <div class="description">单样本预测</div>
                    </div>
                    
                    <div class="endpoint">
                        <span class="method">POST</span> /predict_batch
                        <div class="description">批量预测</div>
                    </div>
                    
                    <div class="endpoint">
                        <span class="method">POST</span> /compare
                        <div class="description">模型比较</div>
                    </div>
                    
                    <div class="endpoint">
                        <span class="method">GET</span> /stats
                        <div class="description">服务器统计信息</div>
                    </div>
                </div>
                
                <div class="api-section">
                    <h2>📖 使用示例</h2>
                    <h3>单样本预测:</h3>
                    <pre><code>curl -X POST http://localhost:5000/predict \\
  -H "Content-Type: application/json" \\
  -d '{"sample": [1.0, 2.0, 3.0, 4.0], "models": ["RandomForest", "XGBoost"]}'</code></pre>
                    
                    <h3>批量预测:</h3>
                    <pre><code>curl -X POST http://localhost:5000/predict_batch \\
  -H "Content-Type: application/json" \\
  -d '{"samples": [[1,2,3,4], [5,6,7,8]], "ensemble_method": "average"}'</code></pre>
                </div>
            </div>
        </body>
        </html>
        """.replace("{{ models_count }}", str(len(self.prediction_api.get_available_models()))) \
           .replace("{{ total_requests }}", str(self.stats["total_requests"])) \
           .replace("{{ success_rate }}", str(round(
               (self.stats["successful_predictions"] / max(1, self.stats["total_requests"])) * 100, 1
           )))
    
    def run(self):
        """启动服务器"""
        try:
            logger.info(f"启动模型服务器: http://{self.host}:{self.port}")
            self.app.run(host=self.host, port=self.port, debug=self.debug)
        except Exception as e:
            logger.error(f"启动服务器失败: {e}")
            raise


# 便捷函数
def start_model_server(host: str = "127.0.0.1", port: int = 5000, debug: bool = False):
    """
    启动模型服务器的便捷函数
    
    Args:
        host: 服务器主机地址
        port: 服务器端口
        debug: 是否开启调试模式
    """
    if not FLASK_AVAILABLE:
        print("错误: Flask未安装，无法启动服务器")
        print("请运行: pip install flask flask-cors")
        return
    
    server = ModelServer(host, port, debug)
    server.run()


class ModelClient:
    """
    模型服务器客户端
    用于与模型服务器进行交互
    """
    
    def __init__(self, server_url: str = "http://127.0.0.1:5000"):
        """
        初始化客户端
        
        Args:
            server_url: 服务器URL
        """
        self.server_url = server_url.rstrip('/')
        
        try:
            import requests
            self.requests = requests
        except ImportError:
            raise ImportError("requests库未安装，请运行: pip install requests")
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            response = self.requests.get(f"{self.server_url}/health")
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_models(self) -> Dict[str, Any]:
        """获取可用模型"""
        try:
            response = self.requests.get(f"{self.server_url}/models")
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def predict(self, sample: List[float], models: Optional[List[str]] = None,
               return_proba: bool = True) -> Dict[str, Any]:
        """单样本预测"""
        try:
            data = {
                "sample": sample,
                "models": models,
                "return_proba": return_proba
            }
            response = self.requests.post(f"{self.server_url}/predict", json=data)
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def predict_batch(self, samples: List[List[float]], models: Optional[List[str]] = None,
                     return_proba: bool = True, ensemble_method: Optional[str] = None) -> Dict[str, Any]:
        """批量预测"""
        try:
            data = {
                "samples": samples,
                "models": models,
                "return_proba": return_proba,
                "ensemble_method": ensemble_method
            }
            response = self.requests.post(f"{self.server_url}/predict_batch", json=data)
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def compare_models(self, samples: List[List[float]], models: Optional[List[str]] = None) -> Dict[str, Any]:
        """模型比较"""
        try:
            data = {
                "samples": samples,
                "models": models
            }
            response = self.requests.post(f"{self.server_url}/compare", json=data)
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_stats(self) -> Dict[str, Any]:
        """获取服务器统计信息"""
        try:
            response = self.requests.get(f"{self.server_url}/stats")
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="机器学习模型服务器")
    parser.add_argument("--host", default="127.0.0.1", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=5000, help="服务器端口")
    parser.add_argument("--debug", action="store_true", help="开启调试模式")
    
    args = parser.parse_args()
    
    print("启动机器学习模型服务器...")
    start_model_server(args.host, args.port, args.debug)
