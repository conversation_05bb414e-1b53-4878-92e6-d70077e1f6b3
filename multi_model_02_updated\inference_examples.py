#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推理功能使用示例
演示如何使用新增的推理模块
"""

import sys
import numpy as np
import pandas as pd
from pathlib import Path

# 添加代码路径
sys.path.append(str(Path(__file__).parent / 'code'))

from model_inference import ModelInference, quick_predict, quick_ensemble_predict
from prediction_api import PredictionAPI, predict, predict_file, get_available_models
from batch_prediction import BatchPredictor, batch_predict_file, batch_predict_files
from model_server import ModelServer, ModelClient, start_model_server


def example_1_basic_inference():
    """示例1: 基础模型推理"""
    print("=" * 60)
    print("示例1: 基础模型推理")
    print("=" * 60)
    
    # 创建推理器
    inference = ModelInference()
    
    # 加载所有可用模型
    print("1. 加载模型...")
    load_results = inference.load_all_available_models()
    print(f"模型加载结果: {load_results}")
    
    # 获取已加载的模型
    loaded_models = inference.get_loaded_models()
    print(f"已加载的模型: {loaded_models}")
    
    if loaded_models:
        # 创建示例数据
        X_sample = np.random.rand(5, 10)  # 5个样本，10个特征
        print(f"示例数据形状: {X_sample.shape}")
        
        # 使用第一个模型进行预测
        model_name = loaded_models[0]
        print(f"\n2. 使用模型 {model_name} 进行预测...")
        predictions = inference.predict(model_name, X_sample, return_proba=True)
        
        if predictions is not None:
            print(f"预测结果: {predictions}")
            print(f"预测结果形状: {predictions.shape}")
        
        # 批量预测（使用多个模型）
        if len(loaded_models) > 1:
            print(f"\n3. 使用多个模型进行批量预测...")
            batch_results = inference.predict_batch(loaded_models[:3], X_sample)
            print(f"批量预测结果: {list(batch_results.keys())}")
            
            # 集成预测
            print(f"\n4. 集成预测...")
            ensemble_pred = inference.ensemble_predict(loaded_models[:3], X_sample, method='average')
            if ensemble_pred is not None:
                print(f"集成预测结果: {ensemble_pred}")


def example_2_prediction_api():
    """示例2: 简化预测API"""
    print("\n" + "=" * 60)
    print("示例2: 简化预测API")
    print("=" * 60)
    
    # 获取可用模型
    print("1. 获取可用模型...")
    models = get_available_models()
    print(f"可用模型: {models}")
    
    if models:
        # 单样本预测
        print("\n2. 单样本预测...")
        sample = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0]
        result = predict(sample, models=models[:2] if len(models) > 1 else models)
        print(f"单样本预测结果: {result}")
        
        # 批量预测
        print("\n3. 批量预测...")
        samples = np.random.rand(10, 10).tolist()  # 10个样本
        batch_result = predict(samples, models=models[:2] if len(models) > 1 else models, 
                              ensemble_method='average')
        print(f"批量预测成功: {batch_result.get('success', False)}")
        if batch_result.get('success'):
            print(f"使用的模型: {batch_result.get('models_used', [])}")
            print(f"样本数量: {batch_result.get('sample_count', 0)}")
        
        # 创建API实例进行模型比较
        print("\n4. 模型比较...")
        api = PredictionAPI()
        comparison_result = api.compare_models(samples, models=models[:3] if len(models) > 2 else models)
        if comparison_result.get('success'):
            print("模型比较统计信息:")
            for model, stats in comparison_result.get('comparison', {}).get('statistics', {}).items():
                print(f"  {model}: 均值={stats['mean']:.4f}, 标准差={stats['std']:.4f}")


def example_3_batch_prediction():
    """示例3: 批量预测"""
    print("\n" + "=" * 60)
    print("示例3: 批量预测")
    print("=" * 60)
    
    # 创建批量预测器
    print("1. 创建批量预测器...")
    predictor = BatchPredictor(chunk_size=100, n_jobs=1)
    
    # 创建示例数据文件
    print("\n2. 创建示例数据文件...")
    sample_data = pd.DataFrame(np.random.rand(500, 10), 
                              columns=[f'feature_{i}' for i in range(10)])
    sample_file = "sample_data_for_batch.csv"
    sample_data.to_csv(sample_file, index=False)
    print(f"示例数据已保存至: {sample_file}")
    
    # 批量预测
    print("\n3. 执行批量预测...")
    models = get_available_models()
    if models:
        result = predictor.predict_large_dataset(
            sample_file, 
            models=models[:2] if len(models) > 1 else models,
            save_intermediate=False
        )
        
        if result.get('success'):
            print(f"批量预测完成:")
            print(f"  总样本数: {result.get('total_samples', 0)}")
            print(f"  处理样本数: {result.get('processed_samples', 0)}")
            print(f"  使用模型: {result.get('models_used', [])}")
            print(f"  处理时间: {result.get('processing_time', 0):.2f}秒")
            print(f"  输出目录: {result.get('output_directory', '')}")
    
    # 清理示例文件
    try:
        Path(sample_file).unlink()
        print(f"\n示例文件 {sample_file} 已清理")
    except:
        pass


def example_4_model_server():
    """示例4: 模型服务器（需要手动启动）"""
    print("\n" + "=" * 60)
    print("示例4: 模型服务器")
    print("=" * 60)
    
    print("模型服务器示例:")
    print("1. 启动服务器:")
    print("   python model_server.py --host 127.0.0.1 --port 5000")
    print("\n2. 或者在代码中启动:")
    print("   from model_server import start_model_server")
    print("   start_model_server()")
    
    print("\n3. 使用客户端进行预测:")
    print("   from model_server import ModelClient")
    print("   client = ModelClient('http://127.0.0.1:5000')")
    print("   result = client.predict([1.0, 2.0, 3.0, 4.0, 5.0])")
    
    print("\n4. 可用的API端点:")
    endpoints = [
        "GET  /health - 健康检查",
        "GET  /models - 获取可用模型",
        "POST /predict - 单样本预测",
        "POST /predict_batch - 批量预测",
        "POST /compare - 模型比较",
        "GET  /stats - 服务器统计"
    ]
    for endpoint in endpoints:
        print(f"   {endpoint}")
    
    print("\n5. curl示例:")
    curl_example = '''curl -X POST http://localhost:5000/predict \\
  -H "Content-Type: application/json" \\
  -d '{"sample": [1.0, 2.0, 3.0, 4.0, 5.0], "models": ["RandomForest"]}'
    '''
    print(curl_example)


def example_5_quick_functions():
    """示例5: 快速函数"""
    print("\n" + "=" * 60)
    print("示例5: 快速函数")
    print("=" * 60)
    
    models = get_available_models()
    if not models:
        print("没有可用的模型，请先训练模型")
        return
    
    # 快速单模型预测
    print("1. 快速单模型预测...")
    X_sample = np.random.rand(3, 10)
    model_name = models[0]
    
    predictions = quick_predict(model_name, X_sample, return_proba=True)
    if predictions is not None:
        print(f"使用 {model_name} 的预测结果: {predictions}")
    
    # 快速集成预测
    if len(models) > 1:
        print("\n2. 快速集成预测...")
        ensemble_models = models[:3] if len(models) > 2 else models
        ensemble_pred = quick_ensemble_predict(ensemble_models, X_sample, method='average')
        if ensemble_pred is not None:
            print(f"集成预测结果: {ensemble_pred}")


def example_6_file_prediction():
    """示例6: 文件预测"""
    print("\n" + "=" * 60)
    print("示例6: 文件预测")
    print("=" * 60)
    
    # 创建示例数据文件
    print("1. 创建示例数据文件...")
    sample_data = pd.DataFrame(np.random.rand(20, 10), 
                              columns=[f'feature_{i}' for i in range(10)])
    sample_file = "sample_data_for_prediction.csv"
    sample_data.to_csv(sample_file, index=False)
    print(f"示例数据已保存至: {sample_file}")
    
    # 文件预测
    print("\n2. 执行文件预测...")
    models = get_available_models()
    if models:
        result = predict_file(
            sample_file,
            models=models[:2] if len(models) > 1 else models,
            save_results=True
        )
        
        if result.get('success'):
            print(f"文件预测完成:")
            print(f"  样本数量: {result.get('sample_count', 0)}")
            print(f"  使用模型: {result.get('models_used', [])}")
            if 'output_file' in result:
                print(f"  结果已保存至: {result['output_file']}")
    
    # 清理示例文件
    try:
        Path(sample_file).unlink()
        print(f"\n示例文件 {sample_file} 已清理")
    except:
        pass


def main():
    """主函数：运行所有示例"""
    print("🤖 机器学习推理功能示例")
    print("本示例演示了新增的四个推理模块的使用方法")
    
    try:
        # 运行各个示例
        example_1_basic_inference()
        example_2_prediction_api()
        example_3_batch_prediction()
        example_4_model_server()
        example_5_quick_functions()
        example_6_file_prediction()
        
        print("\n" + "=" * 60)
        print("✅ 所有示例运行完成！")
        print("=" * 60)
        
        print("\n📚 模块说明:")
        print("1. model_inference.py - 核心推理引擎，提供模型加载和预测功能")
        print("2. prediction_api.py - 简化的预测API，提供高级接口")
        print("3. batch_prediction.py - 批量预测工具，支持大规模数据处理")
        print("4. model_server.py - HTTP服务器，提供REST API接口")
        
        print("\n🚀 快速开始:")
        print("# 简单预测")
        print("from prediction_api import predict")
        print("result = predict([1, 2, 3, 4, 5])")
        print()
        print("# 批量预测")
        print("from batch_prediction import batch_predict_file")
        print("result = batch_predict_file('data.csv')")
        print()
        print("# 启动服务器")
        print("from model_server import start_model_server")
        print("start_model_server()")
        
    except Exception as e:
        print(f"\n❌ 示例运行出错: {e}")
        print("请确保已经训练了模型并且模型缓存文件存在")


if __name__ == "__main__":
    main()
