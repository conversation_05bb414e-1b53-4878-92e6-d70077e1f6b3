2025-08-23 10:57:42 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-23 10:57:42 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-23 10:57:42 - main - INFO - 已确保输出目录结构存在: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output
2025-08-23 10:57:42 - main - INFO - 已保存数据文件路径到缓存: data/test_data.csv
2025-08-23 10:57:42 - config_manager - INFO - 默认配置已初始化
2025-08-23 10:57:42 - config_manager - INFO - 注册预处理器: current_scaler (StandardScaler)
2025-08-23 10:57:42 - main - INFO - 训练和优化 RandomForest
2025-08-23 10:57:42 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-23 10:57:42 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-23 10:57:42 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-23 10:57:42 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-23 10:57:42 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1}
2025-08-23 10:57:43 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.5000
2025-08-23 10:57:44 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9500
2025-08-23 10:57:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-23 10:57:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9500
2025-08-23 10:57:51 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 258, 'max_depth': 8, 'min_samples_split': 5, 'min_samples_leaf': 4, 'max_features': 'log2'}
2025-08-23 10:57:51 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9500
2025-08-23 10:57:51 - hyperparameter_tuning - INFO - 实际执行试验次数: 13/50
2025-08-23 10:57:51 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-23 10:57:51 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-23 10:57:51 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-23 10:57:51 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250823_105751.html
2025-08-23 10:57:52 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-23 10:57:52 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-23 10:57:52 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250823_105752.html
2025-08-23 10:57:52 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 9.69 秒
2025-08-23 10:57:52 - main - INFO - 使用最佳参数重新训练 RandomForest, 最佳分数: 0.9500
2025-08-23 10:57:52 - model_training - INFO - 模型名称: Random Forest
2025-08-23 10:57:52 - model_training - INFO - 准确率: 1.0000
2025-08-23 10:57:52 - model_training - INFO - AUC: 1.0000
2025-08-23 10:57:52 - model_training - INFO - AUPRC: 1.0000
2025-08-23 10:57:52 - model_training - INFO - 混淆矩阵:
2025-08-23 10:57:52 - model_training - INFO - 
[[2 0]
 [0 2]]
2025-08-23 10:57:52 - model_training - INFO - 
分类报告:
2025-08-23 10:57:52 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      1.00      1.00         2
           1       1.00      1.00      1.00         2

    accuracy                           1.00         4
   macro avg       1.00      1.00      1.00         4
weighted avg       1.00      1.00      1.00         4

2025-08-23 10:57:52 - model_training - INFO - 训练时间: 0.53 秒
2025-08-23 10:57:53 - model_training - INFO - 模型 RandomForest 性能: 准确率=1.0000
2025-08-23 10:57:53 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-23 10:57:53 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
