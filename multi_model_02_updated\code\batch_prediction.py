#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量预测工具
提供高效的批量数据预测功能
"""

import os
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Union, Dict, List, Optional, Tuple, Any
import json
import time
from datetime import datetime
import concurrent.futures
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

from prediction_api import PredictionAPI
from model_inference import ModelInference
from config import OUTPUT_PATH
from logger import get_logger

logger = get_logger("batch_prediction")


class BatchPredictor:
    """
    批量预测器
    支持大规模数据的高效批量预测
    """
    
    def __init__(self, chunk_size: int = 1000, n_jobs: int = 1):
        """
        初始化批量预测器
        
        Args:
            chunk_size: 数据块大小
            n_jobs: 并行作业数
        """
        self.chunk_size = chunk_size
        self.n_jobs = n_jobs
        self.api = PredictionAPI()
        self.results_history = []
        
    def predict_large_dataset(self, data_path: str,
                            models: Optional[List[str]] = None,
                            output_dir: Optional[str] = None,
                            return_proba: bool = True,
                            save_intermediate: bool = True) -> Dict[str, Any]:
        """
        预测大型数据集
        
        Args:
            data_path: 数据文件路径
            models: 使用的模型列表
            output_dir: 输出目录
            return_proba: 是否返回预测概率
            save_intermediate: 是否保存中间结果
            
        Returns:
            Dict: 预测结果摘要
        """
        start_time = time.time()
        
        try:
            # 读取数据
            logger.info(f"开始读取数据: {data_path}")
            data = pd.read_csv(data_path)
            total_samples = len(data)
            logger.info(f"数据集大小: {total_samples} 样本")
            
            # 准备输出目录
            if output_dir is None:
                output_dir = OUTPUT_PATH / "batch_predictions" / datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 确定使用的模型
            if models is None:
                models = self.api.get_available_models()
            
            # 分块处理
            chunks = self._split_data_into_chunks(data)
            total_chunks = len(chunks)
            logger.info(f"数据分为 {total_chunks} 个块进行处理")
            
            all_predictions = {model: [] for model in models}
            chunk_results = []
            
            # 处理每个数据块
            for i, chunk in enumerate(tqdm(chunks, desc="处理数据块")):
                chunk_start_time = time.time()
                
                # 预测当前块
                chunk_result = self.api.predict_from_data(
                    chunk, models, return_proba
                )
                
                if chunk_result.get("success"):
                    # 收集预测结果
                    for model in models:
                        if model in chunk_result["predictions"]:
                            all_predictions[model].extend(chunk_result["predictions"][model])
                    
                    chunk_info = {
                        "chunk_id": i,
                        "samples": len(chunk),
                        "processing_time": time.time() - chunk_start_time,
                        "models_used": list(chunk_result["predictions"].keys())
                    }
                    chunk_results.append(chunk_info)
                    
                    # 保存中间结果
                    if save_intermediate:
                        chunk_file = output_dir / f"chunk_{i:04d}_predictions.json"
                        self._save_chunk_result(chunk_result, chunk_file)
                
                else:
                    logger.error(f"块 {i} 预测失败: {chunk_result.get('error', '未知错误')}")
            
            # 合并所有预测结果
            final_predictions = {}
            for model in models:
                if all_predictions[model]:
                    final_predictions[model] = np.array(all_predictions[model])
            
            # 计算统计信息
            processing_time = time.time() - start_time
            
            result = {
                "success": True,
                "total_samples": total_samples,
                "processed_samples": sum(len(pred) for pred in final_predictions.values()) // len(final_predictions) if final_predictions else 0,
                "models_used": list(final_predictions.keys()),
                "processing_time": processing_time,
                "chunks_processed": len(chunk_results),
                "predictions": final_predictions,
                "output_directory": str(output_dir),
                "chunk_results": chunk_results
            }
            
            # 保存最终结果
            final_result_file = output_dir / "final_predictions.json"
            self._save_final_result(result, final_result_file)
            
            # 生成预测报告
            report_file = output_dir / "prediction_report.html"
            self._generate_prediction_report(result, report_file)
            
            logger.info(f"批量预测完成，耗时 {processing_time:.2f} 秒")
            return result
            
        except Exception as e:
            logger.error(f"批量预测失败: {e}")
            return {"success": False, "error": str(e)}
    
    def predict_multiple_files(self, file_paths: List[str],
                              models: Optional[List[str]] = None,
                              output_dir: Optional[str] = None,
                              parallel: bool = True) -> Dict[str, Any]:
        """
        预测多个文件
        
        Args:
            file_paths: 文件路径列表
            models: 使用的模型列表
            output_dir: 输出目录
            parallel: 是否并行处理
            
        Returns:
            Dict: 预测结果摘要
        """
        start_time = time.time()
        
        # 准备输出目录
        if output_dir is None:
            output_dir = OUTPUT_PATH / "multi_file_predictions" / datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        file_results = {}
        
        if parallel and self.n_jobs > 1:
            # 并行处理
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.n_jobs) as executor:
                future_to_file = {
                    executor.submit(self._predict_single_file, file_path, models, output_dir): file_path
                    for file_path in file_paths
                }
                
                for future in tqdm(concurrent.futures.as_completed(future_to_file), 
                                 total=len(file_paths), desc="处理文件"):
                    file_path = future_to_file[future]
                    try:
                        result = future.result()
                        file_results[file_path] = result
                    except Exception as e:
                        logger.error(f"处理文件 {file_path} 失败: {e}")
                        file_results[file_path] = {"success": False, "error": str(e)}
        else:
            # 串行处理
            for file_path in tqdm(file_paths, desc="处理文件"):
                result = self._predict_single_file(file_path, models, output_dir)
                file_results[file_path] = result
        
        # 汇总结果
        successful_files = [f for f, r in file_results.items() if r.get("success")]
        failed_files = [f for f, r in file_results.items() if not r.get("success")]
        
        summary = {
            "success": True,
            "total_files": len(file_paths),
            "successful_files": len(successful_files),
            "failed_files": len(failed_files),
            "processing_time": time.time() - start_time,
            "output_directory": str(output_dir),
            "file_results": file_results,
            "successful_file_list": successful_files,
            "failed_file_list": failed_files
        }
        
        # 保存汇总结果
        summary_file = output_dir / "multi_file_summary.json"
        self._save_summary(summary, summary_file)
        
        logger.info(f"多文件预测完成: {len(successful_files)}/{len(file_paths)} 成功")
        return summary
    
    def predict_streaming_data(self, data_generator,
                              models: Optional[List[str]] = None,
                              output_file: Optional[str] = None,
                              buffer_size: int = 100) -> Dict[str, Any]:
        """
        预测流式数据
        
        Args:
            data_generator: 数据生成器
            models: 使用的模型列表
            output_file: 输出文件
            buffer_size: 缓冲区大小
            
        Returns:
            Dict: 预测结果
        """
        start_time = time.time()
        
        if models is None:
            models = self.api.get_available_models()
        
        all_predictions = {model: [] for model in models}
        buffer = []
        total_processed = 0
        
        try:
            for data_point in data_generator:
                buffer.append(data_point)
                
                # 当缓冲区满时进行预测
                if len(buffer) >= buffer_size:
                    batch_data = np.array(buffer)
                    result = self.api.predict_from_data(batch_data, models)
                    
                    if result.get("success"):
                        for model in models:
                            if model in result["predictions"]:
                                all_predictions[model].extend(result["predictions"][model])
                    
                    total_processed += len(buffer)
                    buffer = []
            
            # 处理剩余数据
            if buffer:
                batch_data = np.array(buffer)
                result = self.api.predict_from_data(batch_data, models)
                
                if result.get("success"):
                    for model in models:
                        if model in result["predictions"]:
                            all_predictions[model].extend(result["predictions"][model])
                
                total_processed += len(buffer)
            
            # 转换为numpy数组
            final_predictions = {}
            for model in models:
                if all_predictions[model]:
                    final_predictions[model] = np.array(all_predictions[model])
            
            result = {
                "success": True,
                "total_processed": total_processed,
                "models_used": list(final_predictions.keys()),
                "processing_time": time.time() - start_time,
                "predictions": final_predictions
            }
            
            # 保存结果
            if output_file:
                self._save_streaming_result(result, output_file)
            
            logger.info(f"流式预测完成，处理 {total_processed} 个样本")
            return result
            
        except Exception as e:
            logger.error(f"流式预测失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _split_data_into_chunks(self, data: pd.DataFrame) -> List[pd.DataFrame]:
        """将数据分割成块"""
        chunks = []
        for i in range(0, len(data), self.chunk_size):
            chunk = data.iloc[i:i + self.chunk_size]
            chunks.append(chunk)
        return chunks
    
    def _predict_single_file(self, file_path: str, models: Optional[List[str]], 
                           output_dir: Path) -> Dict[str, Any]:
        """预测单个文件"""
        try:
            file_name = Path(file_path).stem
            output_file = output_dir / f"{file_name}_predictions.json"
            
            result = self.api.predict_from_file(
                file_path, models, return_proba=True, 
                save_results=True, output_file=str(output_file)
            )
            
            return result
            
        except Exception as e:
            logger.error(f"预测文件 {file_path} 失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _save_chunk_result(self, result: Dict, output_file: Path):
        """保存块结果"""
        try:
            # 转换numpy数组为列表
            json_result = result.copy()
            if "predictions" in json_result:
                json_result["predictions"] = {
                    k: v.tolist() if isinstance(v, np.ndarray) else v
                    for k, v in json_result["predictions"].items()
                }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(json_result, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存块结果失败: {e}")
    
    def _save_final_result(self, result: Dict, output_file: Path):
        """保存最终结果"""
        try:
            # 为了节省空间，不保存完整的预测数组，只保存统计信息
            summary_result = result.copy()
            
            # 计算预测统计信息
            if "predictions" in result:
                prediction_stats = {}
                for model, preds in result["predictions"].items():
                    if isinstance(preds, np.ndarray):
                        prediction_stats[model] = {
                            "count": len(preds),
                            "mean": float(np.mean(preds)),
                            "std": float(np.std(preds)),
                            "min": float(np.min(preds)),
                            "max": float(np.max(preds)),
                            "median": float(np.median(preds))
                        }
                
                summary_result["prediction_statistics"] = prediction_stats
                # 移除原始预测数据以节省空间
                del summary_result["predictions"]
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(summary_result, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存最终结果失败: {e}")
    
    def _save_summary(self, summary: Dict, output_file: Path):
        """保存汇总结果"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存汇总结果失败: {e}")
    
    def _save_streaming_result(self, result: Dict, output_file: str):
        """保存流式预测结果"""
        try:
            json_result = result.copy()
            if "predictions" in json_result:
                json_result["predictions"] = {
                    k: v.tolist() if isinstance(v, np.ndarray) else v
                    for k, v in json_result["predictions"].items()
                }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(json_result, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存流式结果失败: {e}")
    
    def _generate_prediction_report(self, result: Dict, output_file: Path):
        """生成预测报告"""
        try:
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>批量预测报告</title>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ background-color: #f0f0f0; padding: 10px; border-radius: 5px; }}
                    .section {{ margin: 20px 0; }}
                    .stats {{ display: flex; justify-content: space-around; }}
                    .stat-box {{ background-color: #e8f4f8; padding: 15px; border-radius: 5px; text-align: center; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>批量预测报告</h1>
                    <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                
                <div class="section">
                    <h2>预测概览</h2>
                    <div class="stats">
                        <div class="stat-box">
                            <h3>{result.get('total_samples', 0)}</h3>
                            <p>总样本数</p>
                        </div>
                        <div class="stat-box">
                            <h3>{result.get('processed_samples', 0)}</h3>
                            <p>处理样本数</p>
                        </div>
                        <div class="stat-box">
                            <h3>{len(result.get('models_used', []))}</h3>
                            <p>使用模型数</p>
                        </div>
                        <div class="stat-box">
                            <h3>{result.get('processing_time', 0):.2f}s</h3>
                            <p>处理时间</p>
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h2>使用的模型</h2>
                    <ul>
                        {''.join(f'<li>{model}</li>' for model in result.get('models_used', []))}
                    </ul>
                </div>
                
                <div class="section">
                    <h2>处理详情</h2>
                    <p>数据块数量: {result.get('chunks_processed', 0)}</p>
                    <p>输出目录: {result.get('output_directory', '')}</p>
                </div>
            </body>
            </html>
            """
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
                
        except Exception as e:
            logger.error(f"生成预测报告失败: {e}")


# 便捷函数
def batch_predict_file(file_path: str, models: Optional[List[str]] = None,
                      chunk_size: int = 1000, output_dir: Optional[str] = None) -> Dict[str, Any]:
    """
    批量预测文件的便捷函数
    
    Args:
        file_path: 数据文件路径
        models: 使用的模型列表
        chunk_size: 数据块大小
        output_dir: 输出目录
        
    Returns:
        Dict: 预测结果
    """
    predictor = BatchPredictor(chunk_size=chunk_size)
    return predictor.predict_large_dataset(file_path, models, output_dir)


def batch_predict_files(file_paths: List[str], models: Optional[List[str]] = None,
                       parallel: bool = True, output_dir: Optional[str] = None) -> Dict[str, Any]:
    """
    批量预测多个文件的便捷函数
    
    Args:
        file_paths: 文件路径列表
        models: 使用的模型列表
        parallel: 是否并行处理
        output_dir: 输出目录
        
    Returns:
        Dict: 预测结果
    """
    predictor = BatchPredictor()
    return predictor.predict_multiple_files(file_paths, models, output_dir, parallel)


if __name__ == "__main__":
    # 示例用法
    print("批量预测工具示例")
    
    # 创建批量预测器
    predictor = BatchPredictor(chunk_size=500, n_jobs=2)
    
    print("批量预测器已创建，可以开始处理大规模数据")
