#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP服务器使用示例
演示如何启动和使用模型服务器
"""

import sys
from pathlib import Path

# 添加代码路径
sys.path.append(str(Path(__file__).parent / 'code'))

def start_server_example():
    """启动服务器示例"""
    print("=" * 60)
    print("HTTP模型服务器使用指南")
    print("=" * 60)
    
    print("1. 启动服务器的方法:")
    print()
    print("方法A - 命令行启动:")
    print("```bash")
    print("cd multi_model_02_updated")
    print("python code/model_server.py --host 0.0.0.0 --port 5000")
    print("```")
    print()
    print("方法B - 代码启动:")
    print("```python")
    print("from model_server import start_model_server")
    print("start_model_server(host='0.0.0.0', port=5000)")
    print("```")
    print()
    
    print("2. 服务器启动后，访问 http://localhost:5000 查看主页")
    print()
    
    print("3. 可用的API端点:")
    endpoints = [
        ("GET  /health", "健康检查"),
        ("GET  /models", "获取可用模型列表"),
        ("POST /predict", "单样本预测"),
        ("POST /predict_batch", "批量预测"),
        ("POST /compare", "模型比较"),
        ("GET  /stats", "服务器统计信息")
    ]
    
    for endpoint, desc in endpoints:
        print(f"   {endpoint:<20} - {desc}")
    print()
    
    print("4. 使用curl进行预测:")
    print()
    print("单样本预测:")
    print("```bash")
    curl_single = '''curl -X POST http://localhost:5000/predict \\
  -H "Content-Type: application/json" \\
  -d '{"sample": [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16], "models": ["RandomForest"]}'
    '''
    print(curl_single)
    print("```")
    print()
    
    print("批量预测:")
    print("```bash")
    curl_batch = '''curl -X POST http://localhost:5000/predict_batch \\
  -H "Content-Type: application/json" \\
  -d '{"samples": [[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16], [2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17]], "ensemble_method": "average"}'
    '''
    print(curl_batch)
    print("```")
    print()
    
    print("5. 使用Python客户端:")
    print("```python")
    client_code = '''from model_server import ModelClient

# 创建客户端
client = ModelClient('http://localhost:5000')

# 健康检查
health = client.health_check()
print(health)

# 获取模型列表
models = client.get_models()
print(f"可用模型: {models['models']}")

# 单样本预测
sample = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]
result = client.predict(sample, models=['RandomForest'])
print(f"预测结果: {result}")

# 批量预测
samples = [[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16], 
           [2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17]]
batch_result = client.predict_batch(samples, ensemble_method='average')
print(f"批量预测: {batch_result}")
    '''
    print(client_code)
    print("```")


def test_client_example():
    """测试客户端示例"""
    print("\n" + "=" * 60)
    print("客户端测试示例")
    print("=" * 60)
    
    try:
        from model_server import ModelClient
        
        # 创建客户端（假设服务器在运行）
        client = ModelClient('http://localhost:5000')
        
        print("尝试连接服务器...")
        
        # 健康检查
        health = client.health_check()
        if health.get('status') == 'healthy':
            print("✓ 服务器运行正常")
            
            # 获取模型列表
            models_info = client.get_models()
            if models_info.get('success'):
                models = models_info['models']
                print(f"✓ 可用模型: {models}")
                
                # 单样本预测测试
                sample = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]
                result = client.predict(sample, models=models[:2])  # 使用前两个模型
                
                if result.get('success'):
                    print("✓ 单样本预测成功:")
                    for model, pred in result['predictions'].items():
                        print(f"   {model}: {pred:.4f}")
                else:
                    print(f"✗ 单样本预测失败: {result.get('error')}")
                
                # 批量预测测试
                samples = [
                    [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],
                    [2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17]
                ]
                batch_result = client.predict_batch(samples, models=models[:2], ensemble_method='average')
                
                if batch_result.get('success'):
                    print("✓ 批量预测成功:")
                    print(f"   样本数量: {batch_result.get('sample_count')}")
                    if 'ensemble_prediction' in batch_result:
                        ensemble_preds = batch_result['ensemble_prediction']
                        print(f"   集成预测: {ensemble_preds}")
                else:
                    print(f"✗ 批量预测失败: {batch_result.get('error')}")
                    
            else:
                print(f"✗ 获取模型列表失败: {models_info.get('error')}")
        else:
            print("✗ 服务器未运行或连接失败")
            print("请先启动服务器:")
            print("python code/model_server.py")
            
    except ImportError as e:
        if "requests" in str(e):
            print("✗ 需要安装requests库:")
            print("pip install requests")
        else:
            print(f"✗ 导入错误: {e}")
    except Exception as e:
        print(f"✗ 连接服务器失败: {e}")
        print("请确保服务器正在运行:")
        print("python code/model_server.py")


def production_deployment_guide():
    """生产环境部署指南"""
    print("\n" + "=" * 60)
    print("生产环境部署指南")
    print("=" * 60)
    
    print("1. 使用Gunicorn部署 (推荐):")
    print("```bash")
    print("# 安装gunicorn")
    print("pip install gunicorn")
    print()
    print("# 启动服务")
    print("gunicorn -w 4 -b 0.0.0.0:5000 'code.model_server:create_app()'")
    print("```")
    print()
    
    print("2. 使用Docker部署:")
    print("```dockerfile")
    dockerfile_content = '''FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["python", "code/model_server.py", "--host", "0.0.0.0", "--port", "5000"]
    '''
    print(dockerfile_content)
    print("```")
    print()
    
    print("3. 使用systemd服务:")
    print("```ini")
    systemd_content = '''[Unit]
Description=ML Model Server
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/multi_model_02_updated
ExecStart=/usr/bin/python3 code/model_server.py --host 0.0.0.0 --port 5000
Restart=always

[Install]
WantedBy=multi-user.target
    '''
    print(systemd_content)
    print("```")
    print()
    
    print("4. 性能优化建议:")
    print("- 使用多进程/多线程部署")
    print("- 配置负载均衡器 (Nginx)")
    print("- 启用模型缓存")
    print("- 监控内存和CPU使用")
    print("- 设置请求超时和限流")


def main():
    """主函数"""
    print("HTTP模型服务器完整使用指南")
    
    start_server_example()
    test_client_example()
    production_deployment_guide()
    
    print("\n" + "=" * 60)
    print("总结")
    print("=" * 60)
    print("1. 开发测试: python code/model_server.py")
    print("2. 生产部署: 使用Gunicorn或Docker")
    print("3. 客户端调用: 使用ModelClient或curl")
    print("4. 监控状态: 访问/health和/stats端点")
    print()
    print("注意事项:")
    print("- 确保模型已训练并缓存")
    print("- 生产环境建议使用HTTPS")
    print("- 配置适当的安全措施")
    print("- 监控服务器性能和日志")


if __name__ == "__main__":
    main()
