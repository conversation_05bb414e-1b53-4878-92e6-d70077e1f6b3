2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 DecisionTree
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 RandomForest
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 XGBoost
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 LightGBM
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 CatBoost
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 Logistic
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 SVM
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 KNN
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 NaiveBayes
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 NeuralNet
2025-09-02 23:29:12 - model_inference - INFO - 成功加载 10/10 个模型
2025-09-02 23:29:12 - model_inference - WARNING - 应用缩放器失败: X has 10 features, but StandardScaler is expecting 16 features as input.，使用原始数据
2025-09-02 23:29:12 - model_inference - ERROR - 模型 DecisionTree 预测失败: X has 10 features, but DecisionTreeClassifier is expecting 16 features as input.
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 DecisionTree
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 RandomForest
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 XGBoost
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 LightGBM
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 CatBoost
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 Logistic
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 SVM
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 KNN
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 NaiveBayes
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 NeuralNet
2025-09-02 23:29:12 - model_inference - INFO - 成功加载 10/10 个模型
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 DecisionTree
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 RandomForest
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 XGBoost
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 LightGBM
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 CatBoost
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 Logistic
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 SVM
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 KNN
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 NaiveBayes
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 NeuralNet
2025-09-02 23:29:12 - model_inference - INFO - 成功加载 10/10 个模型
2025-09-02 23:29:12 - model_inference - WARNING - 应用缩放器失败: X has 10 features, but StandardScaler is expecting 16 features as input.，使用原始数据
2025-09-02 23:29:12 - model_inference - ERROR - 模型 DecisionTree 预测失败: X has 10 features, but DecisionTreeClassifier is expecting 16 features as input.
2025-09-02 23:29:12 - model_inference - INFO - 完成 0/1 个模型的批量预测
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 DecisionTree
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 RandomForest
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 XGBoost
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 LightGBM
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 CatBoost
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 Logistic
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 SVM
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 KNN
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 NaiveBayes
2025-09-02 23:29:12 - model_inference - INFO - 成功加载模型 NeuralNet
2025-09-02 23:29:12 - model_inference - INFO - 成功加载 10/10 个模型
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 DecisionTree
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 RandomForest
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 XGBoost
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 LightGBM
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 CatBoost
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 Logistic
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 SVM
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 KNN
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 NaiveBayes
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 NeuralNet
2025-09-02 23:45:54 - model_inference - INFO - 成功加载 10/10 个模型
2025-09-02 23:45:54 - model_inference - INFO - 成功加载模型 DecisionTree
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 RandomForest
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 XGBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 LightGBM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 CatBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 Logistic
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 SVM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 KNN
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NaiveBayes
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NeuralNet
2025-09-02 23:45:55 - model_inference - INFO - 成功加载 10/10 个模型
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 1 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 1 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 2/2 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 DecisionTree
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 RandomForest
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 XGBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 LightGBM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 CatBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 Logistic
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 SVM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 KNN
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NaiveBayes
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NeuralNet
2025-09-02 23:45:55 - model_inference - INFO - 成功加载 10/10 个模型
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 DecisionTree
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 RandomForest
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 XGBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 LightGBM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 CatBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 Logistic
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 SVM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 KNN
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NaiveBayes
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NeuralNet
2025-09-02 23:45:55 - model_inference - INFO - 成功加载 10/10 个模型
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 DecisionTree
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 RandomForest
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 XGBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 LightGBM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 CatBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 Logistic
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 SVM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 KNN
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NaiveBayes
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NeuralNet
2025-09-02 23:45:55 - model_inference - INFO - 成功加载 10/10 个模型
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 2/2 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 2/2 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 2/2 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 2/2 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 200 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 2/2 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 DecisionTree
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 RandomForest
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 XGBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 LightGBM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 CatBoost
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 Logistic
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 SVM
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 KNN
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NaiveBayes
2025-09-02 23:45:55 - model_inference - INFO - 成功加载模型 NeuralNet
2025-09-02 23:45:55 - model_inference - INFO - 成功加载 10/10 个模型
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 50 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 50 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 LightGBM 完成 50 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 3/3 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 5 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 5 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 LightGBM 完成 5 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 3/3 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 RandomForest 完成 5 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 XGBoost 完成 5 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 使用模型 LightGBM 完成 5 个样本的预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 3/3 个模型的批量预测
2025-09-02 23:45:55 - model_inference - INFO - 完成 3 个模型的集成预测
