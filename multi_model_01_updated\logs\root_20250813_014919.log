2025-08-13 01:49:19 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-13 01:49:20 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-13 01:49:20 - __main__ - INFO - 🚀 开始集成学习功能测试
2025-08-13 01:49:20 - __main__ - INFO - ==================================================
2025-08-13 01:49:20 - __main__ - INFO - 测试EnsembleClassifier类
2025-08-13 01:49:20 - __main__ - INFO - ==================================================
2025-08-13 01:49:20 - __main__ - INFO - 创建测试数据...
2025-08-13 01:49:20 - __main__ - INFO - 训练集大小: (700, 20)
2025-08-13 01:49:20 - __main__ - INFO - 测试集大小: (300, 20)
2025-08-13 01:49:20 - __main__ - INFO - 类别分布 - 训练集: [350 350]
2025-08-13 01:49:20 - __main__ - INFO - 类别分布 - 测试集: [150 150]
2025-08-13 01:49:20 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-08-13 01:49:20 - model_training - INFO - 模型名称: Random Forest
2025-08-13 01:49:20 - model_training - INFO - 准确率: 0.9633
2025-08-13 01:49:20 - model_training - INFO - AUC: 0.9904
2025-08-13 01:49:20 - model_training - INFO - AUPRC: 0.9925
2025-08-13 01:49:20 - model_training - INFO - 混淆矩阵:
2025-08-13 01:49:20 - model_training - INFO - 
[[144   6]
 [  5 145]]
2025-08-13 01:49:20 - model_training - INFO - 
分类报告:
2025-08-13 01:49:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.97      0.96      0.96       150
           1       0.96      0.97      0.96       150

    accuracy                           0.96       300
   macro avg       0.96      0.96      0.96       300
weighted avg       0.96      0.96      0.96       300

2025-08-13 01:49:20 - model_training - INFO - 训练时间: 0.21 秒
2025-08-13 01:49:20 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9633
2025-08-13 01:49:20 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-13 01:49:20 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-13 01:49:20 - model_ensemble - INFO -   RandomForest 训练完成
2025-08-13 01:49:20 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-08-13 01:49:20 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.000 以处理不平衡
2025-08-13 01:49:20 - model_training - INFO - 模型名称: XGBoost
2025-08-13 01:49:20 - model_training - INFO - 准确率: 0.9633
2025-08-13 01:49:20 - model_training - INFO - AUC: 0.9891
2025-08-13 01:49:20 - model_training - INFO - AUPRC: 0.9927
2025-08-13 01:49:20 - model_training - INFO - 混淆矩阵:
2025-08-13 01:49:20 - model_training - INFO - 
[[144   6]
 [  5 145]]
2025-08-13 01:49:20 - model_training - INFO - 
分类报告:
2025-08-13 01:49:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.97      0.96      0.96       150
           1       0.96      0.97      0.96       150

    accuracy                           0.96       300
   macro avg       0.96      0.96      0.96       300
weighted avg       0.96      0.96      0.96       300

2025-08-13 01:49:20 - model_training - INFO - 训练时间: 0.17 秒
2025-08-13 01:49:20 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9633
2025-08-13 01:49:20 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-13 01:49:20 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-13 01:49:20 - model_ensemble - INFO -   XGBoost 训练完成
2025-08-13 01:49:20 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-08-13 01:49:20 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.000 以处理不平衡
2025-08-13 01:49:25 - model_training - INFO - 模型名称: LightGBM
2025-08-13 01:49:25 - model_training - INFO - 准确率: 0.9633
2025-08-13 01:49:25 - model_training - INFO - AUC: 0.9892
2025-08-13 01:49:25 - model_training - INFO - AUPRC: 0.9931
2025-08-13 01:49:25 - model_training - INFO - 混淆矩阵:
2025-08-13 01:49:25 - model_training - INFO - 
[[142   8]
 [  3 147]]
2025-08-13 01:49:25 - model_training - INFO - 
分类报告:
2025-08-13 01:49:25 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.98      0.95      0.96       150
           1       0.95      0.98      0.96       150

    accuracy                           0.96       300
   macro avg       0.96      0.96      0.96       300
weighted avg       0.96      0.96      0.96       300

2025-08-13 01:49:25 - model_training - INFO - 训练时间: 4.86 秒
2025-08-13 01:49:25 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.9633
2025-08-13 01:49:25 - model_training - INFO - 模型 LightGBM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-13 01:49:25 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-08-13 01:49:25 - model_ensemble - INFO -   LightGBM 训练完成
2025-08-13 01:49:25 - __main__ - INFO - 成功创建 3 个基础模型
2025-08-13 01:49:25 - __main__ - INFO - 测试集成方法: voting
2025-08-13 01:49:25 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 01:49:26 - __main__ - INFO -   voting 准确率: 0.9733
2025-08-13 01:49:26 - __main__ - INFO - 测试集成方法: bagging
2025-08-13 01:49:26 - model_ensemble - INFO - 开始训练集成模型，方法: bagging
2025-08-13 01:49:26 - __main__ - ERROR -   bagging 测试失败: BaggingClassifier.__init__() got an unexpected keyword argument 'base_estimator'
2025-08-13 01:49:26 - __main__ - INFO - 测试集成方法: boosting
2025-08-13 01:49:26 - model_ensemble - INFO - 开始训练集成模型，方法: boosting
2025-08-13 01:49:26 - __main__ - ERROR -   boosting 测试失败: AdaBoostClassifier.__init__() got an unexpected keyword argument 'base_estimator'
2025-08-13 01:49:26 - __main__ - INFO - 测试集成方法: stacking
2025-08-13 01:49:26 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-08-13 01:49:28 - __main__ - INFO -   stacking 准确率: 0.9733
2025-08-13 01:49:28 - __main__ - INFO - ==================================================
2025-08-13 01:49:28 - __main__ - INFO - 测试集成学习管道
2025-08-13 01:49:28 - __main__ - INFO - ==================================================
2025-08-13 01:49:28 - __main__ - INFO - 创建测试数据...
2025-08-13 01:49:28 - __main__ - INFO - 训练集大小: (700, 20)
2025-08-13 01:49:28 - __main__ - INFO - 测试集大小: (300, 20)
2025-08-13 01:49:28 - __main__ - INFO - 类别分布 - 训练集: [350 350]
2025-08-13 01:49:28 - __main__ - INFO - 类别分布 - 测试集: [150 150]
2025-08-13 01:49:28 - model_ensemble - INFO - ============================================================
2025-08-13 01:49:28 - model_ensemble - INFO - 开始运行集成学习管道
2025-08-13 01:49:28 - model_ensemble - INFO - ============================================================
2025-08-13 01:49:28 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM']
2025-08-13 01:49:28 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-08-13 01:49:28 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-08-13 01:49:28 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-08-13 01:49:28 - model_training - INFO - 模型名称: Random Forest
2025-08-13 01:49:28 - model_training - INFO - 准确率: 0.9633
2025-08-13 01:49:28 - model_training - INFO - AUC: 0.9904
2025-08-13 01:49:28 - model_training - INFO - AUPRC: 0.9925
2025-08-13 01:49:28 - model_training - INFO - 混淆矩阵:
2025-08-13 01:49:28 - model_training - INFO - 
[[144   6]
 [  5 145]]
2025-08-13 01:49:28 - model_training - INFO - 
分类报告:
2025-08-13 01:49:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.97      0.96      0.96       150
           1       0.96      0.97      0.96       150

    accuracy                           0.96       300
   macro avg       0.96      0.96      0.96       300
weighted avg       0.96      0.96      0.96       300

2025-08-13 01:49:28 - model_training - INFO - 训练时间: 0.25 秒
2025-08-13 01:49:28 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9633
2025-08-13 01:49:28 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-13 01:49:28 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-13 01:49:28 - model_ensemble - INFO -   RandomForest 训练完成
2025-08-13 01:49:28 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-08-13 01:49:28 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.000 以处理不平衡
2025-08-13 01:49:28 - model_training - INFO - 模型名称: XGBoost
2025-08-13 01:49:28 - model_training - INFO - 准确率: 0.9633
2025-08-13 01:49:28 - model_training - INFO - AUC: 0.9891
2025-08-13 01:49:28 - model_training - INFO - AUPRC: 0.9927
2025-08-13 01:49:28 - model_training - INFO - 混淆矩阵:
2025-08-13 01:49:28 - model_training - INFO - 
[[144   6]
 [  5 145]]
2025-08-13 01:49:28 - model_training - INFO - 
分类报告:
2025-08-13 01:49:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.97      0.96      0.96       150
           1       0.96      0.97      0.96       150

    accuracy                           0.96       300
   macro avg       0.96      0.96      0.96       300
weighted avg       0.96      0.96      0.96       300

2025-08-13 01:49:28 - model_training - INFO - 训练时间: 0.06 秒
2025-08-13 01:49:28 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9633
2025-08-13 01:49:28 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-13 01:49:28 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-13 01:49:28 - model_ensemble - INFO -   XGBoost 训练完成
2025-08-13 01:49:28 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-08-13 01:49:28 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.000 以处理不平衡
2025-08-13 01:49:28 - model_training - INFO - 模型名称: LightGBM
2025-08-13 01:49:28 - model_training - INFO - 准确率: 0.9633
2025-08-13 01:49:28 - model_training - INFO - AUC: 0.9892
2025-08-13 01:49:28 - model_training - INFO - AUPRC: 0.9931
2025-08-13 01:49:28 - model_training - INFO - 混淆矩阵:
2025-08-13 01:49:28 - model_training - INFO - 
[[142   8]
 [  3 147]]
2025-08-13 01:49:28 - model_training - INFO - 
分类报告:
2025-08-13 01:49:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.98      0.95      0.96       150
           1       0.95      0.98      0.96       150

    accuracy                           0.96       300
   macro avg       0.96      0.96      0.96       300
weighted avg       0.96      0.96      0.96       300

2025-08-13 01:49:28 - model_training - INFO - 训练时间: 0.07 秒
2025-08-13 01:49:28 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.9633
2025-08-13 01:49:28 - model_training - INFO - 模型 LightGBM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-13 01:49:28 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-08-13 01:49:28 - model_ensemble - INFO -   LightGBM 训练完成
2025-08-13 01:49:28 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-08-13 01:49:28 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-08-13 01:49:28 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-08-13 01:49:28 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 01:49:28 - model_ensemble - INFO -     voting_soft - 准确率: 0.9733, F1: 0.9733
2025-08-13 01:49:28 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-08-13 01:49:28 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 01:49:29 - model_ensemble - INFO -     voting_hard - 准确率: 0.9667, F1: 0.9667
2025-08-13 01:49:29 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-08-13 01:49:29 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-08-13 01:49:31 - model_ensemble - INFO -   stacking - 准确率: 0.9733, F1: 0.9733
2025-08-13 01:49:31 - model_ensemble - INFO - ============================================================
2025-08-13 01:49:31 - model_ensemble - INFO - 集成学习结果总结
2025-08-13 01:49:31 - model_ensemble - INFO - ============================================================
2025-08-13 01:49:31 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-08-13 01:49:31 - model_ensemble - INFO - 最佳F1分数: 0.9733
2025-08-13 01:49:31 - model_ensemble - INFO - 最佳准确率: 0.9733
2025-08-13 01:49:31 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-08-13 01:49:31 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9733, 精确率: 0.9737, 召回率: 0.9733, F1: 0.9733, AUC: 0.9922
2025-08-13 01:49:31 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9667, 精确率: 0.9667, 召回率: 0.9667, F1: 0.9667, AUC: 0.0000
2025-08-13 01:49:31 - model_ensemble - INFO -   stacking        - 准确率: 0.9733, 精确率: 0.9737, 召回率: 0.9733, F1: 0.9733, AUC: 0.9922
2025-08-13 01:49:31 - __main__ - INFO - 集成学习管道测试成功
2025-08-13 01:49:31 - __main__ - INFO - 生成了 3 个集成模型
2025-08-13 01:49:31 - __main__ - INFO -   voting_soft: 准确率=0.9733, F1=0.9733
2025-08-13 01:49:31 - __main__ - INFO -   voting_hard: 准确率=0.9667, F1=0.9667
2025-08-13 01:49:31 - __main__ - INFO -   stacking: 准确率=0.9733, F1=0.9733
2025-08-13 01:49:31 - __main__ - INFO - ==================================================
2025-08-13 01:49:31 - __main__ - INFO - 测试可视化功能
2025-08-13 01:49:31 - __main__ - INFO - ==================================================
2025-08-13 01:49:31 - __main__ - INFO - 创建测试数据...
2025-08-13 01:49:31 - __main__ - INFO - 训练集大小: (700, 20)
2025-08-13 01:49:31 - __main__ - INFO - 测试集大小: (300, 20)
2025-08-13 01:49:31 - __main__ - INFO - 类别分布 - 训练集: [350 350]
2025-08-13 01:49:31 - __main__ - INFO - 类别分布 - 测试集: [150 150]
2025-08-13 01:49:31 - model_ensemble - INFO - ============================================================
2025-08-13 01:49:31 - model_ensemble - INFO - 开始运行集成学习管道
2025-08-13 01:49:31 - model_ensemble - INFO - ============================================================
2025-08-13 01:49:31 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-08-13 01:49:31 - model_ensemble - INFO - 集成方法: ['voting']
2025-08-13 01:49:31 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-08-13 01:49:31 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-08-13 01:49:31 - model_training - INFO - 模型名称: Random Forest
2025-08-13 01:49:31 - model_training - INFO - 准确率: 0.9633
2025-08-13 01:49:31 - model_training - INFO - AUC: 0.9904
2025-08-13 01:49:31 - model_training - INFO - AUPRC: 0.9925
2025-08-13 01:49:31 - model_training - INFO - 混淆矩阵:
2025-08-13 01:49:31 - model_training - INFO - 
[[144   6]
 [  5 145]]
2025-08-13 01:49:31 - model_training - INFO - 
分类报告:
2025-08-13 01:49:31 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.97      0.96      0.96       150
           1       0.96      0.97      0.96       150

    accuracy                           0.96       300
   macro avg       0.96      0.96      0.96       300
weighted avg       0.96      0.96      0.96       300

2025-08-13 01:49:31 - model_training - INFO - 训练时间: 0.25 秒
2025-08-13 01:49:31 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9633
2025-08-13 01:49:31 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-13 01:49:31 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-13 01:49:31 - model_ensemble - INFO -   RandomForest 训练完成
2025-08-13 01:49:31 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-08-13 01:49:31 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.000 以处理不平衡
2025-08-13 01:49:31 - model_training - INFO - 模型名称: XGBoost
2025-08-13 01:49:31 - model_training - INFO - 准确率: 0.9633
2025-08-13 01:49:31 - model_training - INFO - AUC: 0.9891
2025-08-13 01:49:31 - model_training - INFO - AUPRC: 0.9927
2025-08-13 01:49:31 - model_training - INFO - 混淆矩阵:
2025-08-13 01:49:31 - model_training - INFO - 
[[144   6]
 [  5 145]]
2025-08-13 01:49:31 - model_training - INFO - 
分类报告:
2025-08-13 01:49:31 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.97      0.96      0.96       150
           1       0.96      0.97      0.96       150

    accuracy                           0.96       300
   macro avg       0.96      0.96      0.96       300
weighted avg       0.96      0.96      0.96       300

2025-08-13 01:49:31 - model_training - INFO - 训练时间: 0.06 秒
2025-08-13 01:49:31 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9633
2025-08-13 01:49:31 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-13 01:49:31 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-13 01:49:31 - model_ensemble - INFO -   XGBoost 训练完成
2025-08-13 01:49:31 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-08-13 01:49:31 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-08-13 01:49:31 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-08-13 01:49:31 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 01:49:31 - model_ensemble - INFO -     voting_soft - 准确率: 0.9567, F1: 0.9567
2025-08-13 01:49:31 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-08-13 01:49:31 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 01:49:32 - model_ensemble - INFO -     voting_hard - 准确率: 0.9700, F1: 0.9700
2025-08-13 01:49:32 - model_ensemble - INFO - ============================================================
2025-08-13 01:49:32 - model_ensemble - INFO - 集成学习结果总结
2025-08-13 01:49:32 - model_ensemble - INFO - ============================================================
2025-08-13 01:49:32 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-08-13 01:49:32 - model_ensemble - INFO - 最佳F1分数: 0.9700
2025-08-13 01:49:32 - model_ensemble - INFO - 最佳准确率: 0.9700
2025-08-13 01:49:32 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-08-13 01:49:32 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9567, 精确率: 0.9572, 召回率: 0.9567, F1: 0.9567, AUC: 0.9915
2025-08-13 01:49:32 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9700, 精确率: 0.9705, 召回率: 0.9700, F1: 0.9700, AUC: 0.0000
2025-08-13 01:49:32 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-08-13 01:49:32 - plot_ensemble - INFO - Starting to generate ensemble learning visualization charts
2025-08-13 01:49:32 - plot_ensemble - INFO - Generating performance comparison chart
2025-08-13 01:49:32 - safe_visualization - INFO - Safe matplotlib configuration applied
2025-08-13 01:49:32 - safe_visualization - INFO - Ensemble performance plot saved to: test_output\performance_comparison.png
2025-08-13 01:49:32 - plot_ensemble - INFO - Generating summary report
2025-08-13 01:49:32 - safe_visualization - INFO - Summary report saved to: test_output\ensemble_summary_report.txt
2025-08-13 01:49:32 - plot_ensemble - INFO - Performance comparison chart generated successfully
2025-08-13 01:49:32 - plot_ensemble - INFO - Summary report generated successfully
2025-08-13 01:49:32 - plot_ensemble - INFO - All visualization charts saved to: test_output
2025-08-13 01:49:32 - __main__ - INFO - 可视化功能测试成功
2025-08-13 01:49:32 - __main__ - INFO - ==================================================
2025-08-13 01:49:32 - __main__ - INFO - 测试结果总结
2025-08-13 01:49:32 - __main__ - INFO - ==================================================
2025-08-13 01:49:32 - __main__ - INFO - EnsembleClassifier类: ✅ 通过
2025-08-13 01:49:32 - __main__ - INFO - 集成学习管道: ✅ 通过
2025-08-13 01:49:32 - __main__ - INFO - 可视化功能: ✅ 通过
2025-08-13 01:49:32 - __main__ - INFO - 
总体结果: 3/3 测试通过
2025-08-13 01:49:32 - __main__ - INFO - 🎉 所有测试通过！集成学习功能正常工作
