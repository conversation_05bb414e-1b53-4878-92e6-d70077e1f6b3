# 机器学习推理功能使用说明

## 概述

本项目新增了四个推理模块，完善了模型推理和外部数据测试功能：

1. **model_inference.py** - 核心推理引擎
2. **prediction_api.py** - 简化预测API
3. **batch_prediction.py** - 批量预测工具
4. **model_server.py** - HTTP服务器接口

## 功能特点

### ✅ 已有功能
- ✅ 模型训练和超参数调优
- ✅ 外部数据验证 (`external_validation.py`)
- ✅ 集成学习和多数据源集成
- ✅ 完整的GUI界面
- ✅ 模型性能评估和可视化

### 🆕 新增功能
- 🆕 统一的模型推理接口
- 🆕 简化的预测API
- 🆕 大规模批量预测
- 🆕 HTTP服务器部署
- 🆕 流式数据预测
- 🆕 模型集成预测

## 快速开始

### 1. 基础预测

```python
from prediction_api import predict

# 单样本预测
sample = [1.0, 2.0, 3.0, 4.0, 5.0]
result = predict(sample)
print(result)

# 批量预测
samples = [[1,2,3,4,5], [6,7,8,9,10]]
result = predict(samples, ensemble_method='average')
print(result)
```

### 2. 文件预测

```python
from prediction_api import predict_file

# 预测CSV文件
result = predict_file('data.csv', save_results=True)
print(f"预测完成，结果保存至: {result.get('output_file')}")
```

### 3. 批量处理大文件

```python
from batch_prediction import batch_predict_file

# 处理大型数据集
result = batch_predict_file('large_data.csv', chunk_size=1000)
print(f"处理了 {result.get('processed_samples')} 个样本")
```

### 4. 启动HTTP服务器

```bash
# 命令行启动
python model_server.py --host 0.0.0.0 --port 5000

# 或在代码中启动
from model_server import start_model_server
start_model_server()
```

## 详细使用指南

### 模型推理引擎 (model_inference.py)

核心推理引擎，提供底层的模型加载和预测功能。

```python
from model_inference import ModelInference

# 创建推理器
inference = ModelInference()

# 加载所有可用模型
load_results = inference.load_all_available_models()
print(f"已加载模型: {inference.get_loaded_models()}")

# 单模型预测
predictions = inference.predict('RandomForest', X_data, return_proba=True)

# 批量预测
batch_results = inference.predict_batch(['RandomForest', 'XGBoost'], X_data)

# 集成预测
ensemble_pred = inference.ensemble_predict(['RandomForest', 'XGBoost'], X_data, method='average')
```

### 简化预测API (prediction_api.py)

提供高级的、易用的预测接口。

```python
from prediction_api import PredictionAPI, predict, get_available_models

# 获取可用模型
models = get_available_models()
print(f"可用模型: {models}")

# 创建API实例
api = PredictionAPI()

# 单样本预测
result = api.predict_single_sample([1,2,3,4,5], models=['RandomForest'])

# 从数据预测
result = api.predict_from_data(X_data, models=['RandomForest', 'XGBoost'])

# 模型比较
comparison = api.compare_models(X_data, models=['RandomForest', 'XGBoost'])
```

### 批量预测工具 (batch_prediction.py)

支持大规模数据的高效批量预测。

```python
from batch_prediction import BatchPredictor

# 创建批量预测器
predictor = BatchPredictor(chunk_size=1000, n_jobs=2)

# 预测大型数据集
result = predictor.predict_large_dataset(
    'large_data.csv',
    models=['RandomForest', 'XGBoost'],
    save_intermediate=True
)

# 预测多个文件
file_results = predictor.predict_multiple_files(
    ['file1.csv', 'file2.csv', 'file3.csv'],
    parallel=True
)

# 流式数据预测
def data_generator():
    for i in range(1000):
        yield np.random.rand(10)

streaming_result = predictor.predict_streaming_data(
    data_generator(),
    buffer_size=100
)
```

### HTTP服务器 (model_server.py)

提供REST API接口进行模型预测。

#### 启动服务器

```python
from model_server import start_model_server

# 启动服务器
start_model_server(host='0.0.0.0', port=5000, debug=False)
```

#### API端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/health` | GET | 健康检查 |
| `/models` | GET | 获取可用模型列表 |
| `/predict` | POST | 单样本预测 |
| `/predict_batch` | POST | 批量预测 |
| `/compare` | POST | 模型比较 |
| `/stats` | GET | 服务器统计信息 |

#### 使用客户端

```python
from model_server import ModelClient

# 创建客户端
client = ModelClient('http://localhost:5000')

# 健康检查
health = client.health_check()

# 获取模型列表
models = client.get_models()

# 单样本预测
result = client.predict([1,2,3,4,5], models=['RandomForest'])

# 批量预测
batch_result = client.predict_batch(
    [[1,2,3,4,5], [6,7,8,9,10]], 
    ensemble_method='average'
)
```

#### curl示例

```bash
# 单样本预测
curl -X POST http://localhost:5000/predict \
  -H "Content-Type: application/json" \
  -d '{"sample": [1.0, 2.0, 3.0, 4.0, 5.0], "models": ["RandomForest"]}'

# 批量预测
curl -X POST http://localhost:5000/predict_batch \
  -H "Content-Type: application/json" \
  -d '{"samples": [[1,2,3,4,5], [6,7,8,9,10]], "ensemble_method": "average"}'
```

## 安装依赖

### 必需依赖
```bash
pip install numpy pandas joblib scikit-learn
```

### 可选依赖（用于HTTP服务器）
```bash
pip install flask flask-cors requests tqdm
```

## 测试和示例

### 运行测试
```bash
python test_inference_modules.py
```

### 查看示例
```bash
python inference_examples.py
```

## 注意事项

1. **特征维度匹配**: 确保预测数据的特征数量与训练时一致
2. **模型缓存**: 模型需要先训练并缓存才能进行推理
3. **内存管理**: 处理大型数据集时注意内存使用
4. **并发限制**: HTTP服务器默认为单线程，生产环境建议使用WSGI服务器

## 故障排除

### 常见问题

1. **特征数量不匹配**
   ```
   错误: X has 10 features, but model is expecting 16 features
   解决: 确保输入数据特征数量与训练数据一致
   ```

2. **模型未找到**
   ```
   错误: 模型缓存文件不存在
   解决: 先运行 python run.py --mode train 训练模型
   ```

3. **依赖缺失**
   ```
   错误: Flask未安装
   解决: pip install flask flask-cors
   ```

## 性能优化建议

1. **批量处理**: 使用批量预测而不是逐个预测
2. **模型缓存**: 避免重复加载模型
3. **并行处理**: 启用多进程处理大型数据集
4. **内存管理**: 使用数据块处理超大文件
5. **GPU加速**: 对支持的模型启用GPU加速

## 扩展功能

项目支持以下扩展：

- 自定义预测后处理
- 模型版本管理
- 预测结果缓存
- 异步预测处理
- 模型A/B测试
- 预测监控和日志

## 联系支持

如有问题或建议，请查看项目文档或提交issue。
