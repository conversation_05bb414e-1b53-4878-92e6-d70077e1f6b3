2025-08-13 01:49:20 - __main__ - INFO - 🚀 开始集成学习功能测试
2025-08-13 01:49:20 - __main__ - INFO - ==================================================
2025-08-13 01:49:20 - __main__ - INFO - 测试EnsembleClassifier类
2025-08-13 01:49:20 - __main__ - INFO - ==================================================
2025-08-13 01:49:20 - __main__ - INFO - 创建测试数据...
2025-08-13 01:49:20 - __main__ - INFO - 训练集大小: (700, 20)
2025-08-13 01:49:20 - __main__ - INFO - 测试集大小: (300, 20)
2025-08-13 01:49:20 - __main__ - INFO - 类别分布 - 训练集: [350 350]
2025-08-13 01:49:20 - __main__ - INFO - 类别分布 - 测试集: [150 150]
2025-08-13 01:49:25 - __main__ - INFO - 成功创建 3 个基础模型
2025-08-13 01:49:25 - __main__ - INFO - 测试集成方法: voting
2025-08-13 01:49:26 - __main__ - INFO -   voting 准确率: 0.9733
2025-08-13 01:49:26 - __main__ - INFO - 测试集成方法: bagging
2025-08-13 01:49:26 - __main__ - ERROR -   bagging 测试失败: BaggingClassifier.__init__() got an unexpected keyword argument 'base_estimator'
2025-08-13 01:49:26 - __main__ - INFO - 测试集成方法: boosting
2025-08-13 01:49:26 - __main__ - ERROR -   boosting 测试失败: AdaBoostClassifier.__init__() got an unexpected keyword argument 'base_estimator'
2025-08-13 01:49:26 - __main__ - INFO - 测试集成方法: stacking
2025-08-13 01:49:28 - __main__ - INFO -   stacking 准确率: 0.9733
2025-08-13 01:49:28 - __main__ - INFO - ==================================================
2025-08-13 01:49:28 - __main__ - INFO - 测试集成学习管道
2025-08-13 01:49:28 - __main__ - INFO - ==================================================
2025-08-13 01:49:28 - __main__ - INFO - 创建测试数据...
2025-08-13 01:49:28 - __main__ - INFO - 训练集大小: (700, 20)
2025-08-13 01:49:28 - __main__ - INFO - 测试集大小: (300, 20)
2025-08-13 01:49:28 - __main__ - INFO - 类别分布 - 训练集: [350 350]
2025-08-13 01:49:28 - __main__ - INFO - 类别分布 - 测试集: [150 150]
2025-08-13 01:49:31 - __main__ - INFO - 集成学习管道测试成功
2025-08-13 01:49:31 - __main__ - INFO - 生成了 3 个集成模型
2025-08-13 01:49:31 - __main__ - INFO -   voting_soft: 准确率=0.9733, F1=0.9733
2025-08-13 01:49:31 - __main__ - INFO -   voting_hard: 准确率=0.9667, F1=0.9667
2025-08-13 01:49:31 - __main__ - INFO -   stacking: 准确率=0.9733, F1=0.9733
2025-08-13 01:49:31 - __main__ - INFO - ==================================================
2025-08-13 01:49:31 - __main__ - INFO - 测试可视化功能
2025-08-13 01:49:31 - __main__ - INFO - ==================================================
2025-08-13 01:49:31 - __main__ - INFO - 创建测试数据...
2025-08-13 01:49:31 - __main__ - INFO - 训练集大小: (700, 20)
2025-08-13 01:49:31 - __main__ - INFO - 测试集大小: (300, 20)
2025-08-13 01:49:31 - __main__ - INFO - 类别分布 - 训练集: [350 350]
2025-08-13 01:49:31 - __main__ - INFO - 类别分布 - 测试集: [150 150]
2025-08-13 01:49:32 - __main__ - INFO - 可视化功能测试成功
2025-08-13 01:49:32 - __main__ - INFO - ==================================================
2025-08-13 01:49:32 - __main__ - INFO - 测试结果总结
2025-08-13 01:49:32 - __main__ - INFO - ==================================================
2025-08-13 01:49:32 - __main__ - INFO - EnsembleClassifier类: ✅ 通过
2025-08-13 01:49:32 - __main__ - INFO - 集成学习管道: ✅ 通过
2025-08-13 01:49:32 - __main__ - INFO - 可视化功能: ✅ 通过
2025-08-13 01:49:32 - __main__ - INFO - 
总体结果: 3/3 测试通过
2025-08-13 01:49:32 - __main__ - INFO - 🎉 所有测试通过！集成学习功能正常工作
2025-08-13 01:50:23 - __main__ - INFO - ============================================================
2025-08-13 01:50:23 - __main__ - INFO - 测试AdaBoost修复效果
2025-08-13 01:50:23 - __main__ - INFO - ============================================================
2025-08-13 01:50:23 - __main__ - INFO - 训练集大小: (350, 10)
2025-08-13 01:50:23 - __main__ - INFO - 测试集大小: (150, 10)
2025-08-13 01:50:23 - __main__ - INFO - 开始测试包含AdaBoost的集成学习...
2025-08-13 01:50:23 - __main__ - INFO - ✅ AdaBoost集成测试成功！
2025-08-13 01:50:23 - __main__ - INFO - 生成了 2 个集成模型
2025-08-13 01:50:23 - __main__ - INFO -   voting_soft: 准确率=0.9933, F1=0.9933
2025-08-13 01:50:23 - __main__ - INFO -   voting_hard: 准确率=0.9867, F1=0.9867
2025-08-13 01:50:52 - __main__ - INFO - ============================================================
2025-08-13 01:50:52 - __main__ - INFO - 测试AdaBoost修复效果
2025-08-13 01:50:52 - __main__ - INFO - ============================================================
2025-08-13 01:50:52 - __main__ - INFO - 训练集大小: (350, 10)
2025-08-13 01:50:52 - __main__ - INFO - 测试集大小: (150, 10)
2025-08-13 01:50:52 - __main__ - INFO - 开始测试包含AdaBoost的集成学习...
2025-08-13 01:50:53 - __main__ - INFO - ✅ AdaBoost集成测试成功！
2025-08-13 01:50:53 - __main__ - INFO - 生成了 3 个集成模型
2025-08-13 01:50:53 - __main__ - INFO -   voting_soft: 准确率=0.9933, F1=0.9933
2025-08-13 01:50:53 - __main__ - INFO -   voting_hard: 准确率=0.9867, F1=0.9867
2025-08-13 01:50:53 - __main__ - INFO -   boosting: 准确率=0.9400, F1=0.9399
2025-08-13 01:50:53 - __main__ - ERROR - ❌ AdaBoost集成测试出错: 'AdaBoostClassifier' object has no attribute 'base_estimator'
2025-08-13 01:51:22 - __main__ - INFO - ============================================================
2025-08-13 01:51:22 - __main__ - INFO - 测试AdaBoost修复效果
2025-08-13 01:51:22 - __main__ - INFO - ============================================================
2025-08-13 01:51:22 - __main__ - INFO - 训练集大小: (350, 10)
2025-08-13 01:51:22 - __main__ - INFO - 测试集大小: (150, 10)
2025-08-13 01:51:22 - __main__ - INFO - 开始测试包含AdaBoost的集成学习...
2025-08-13 01:51:23 - __main__ - INFO - ✅ AdaBoost集成测试成功！
2025-08-13 01:51:23 - __main__ - INFO - 生成了 3 个集成模型
2025-08-13 01:51:23 - __main__ - INFO -   voting_soft: 准确率=0.9933, F1=0.9933
2025-08-13 01:51:23 - __main__ - INFO -   voting_hard: 准确率=0.9867, F1=0.9867
2025-08-13 01:51:23 - __main__ - INFO -   boosting: 准确率=0.9400, F1=0.9399
2025-08-13 01:51:23 - __main__ - INFO -   AdaBoost基学习器类型: DecisionTreeClassifier
2025-08-13 01:51:23 - __main__ - INFO -   基学习器max_depth: 1
2025-08-13 02:01:16 - __main__ - INFO - ================================================================================
2025-08-13 02:01:16 - __main__ - INFO - 测试统一集成结果对象功能
2025-08-13 02:01:16 - __main__ - INFO - ================================================================================
2025-08-13 02:01:16 - __main__ - INFO - 训练集大小: (210, 8)
2025-08-13 02:01:16 - __main__ - INFO - 测试集大小: (90, 8)
2025-08-13 02:01:16 - __main__ - INFO - 开始测试集成学习管道...
2025-08-13 02:01:16 - __main__ - INFO - ✅ 集成学习管道测试成功！
2025-08-13 02:01:16 - __main__ - INFO - 生成了 2 个集成模型
2025-08-13 02:01:16 - __main__ - INFO - 
验证新增指标:
2025-08-13 02:01:16 - __main__ - INFO -   voting_soft:
2025-08-13 02:01:16 - __main__ - INFO -     准确率: 0.9889
2025-08-13 02:01:16 - __main__ - INFO -     F1分数: 0.9889
2025-08-13 02:01:16 - __main__ - INFO -     AUC: 1.0000
2025-08-13 02:01:16 - __main__ - INFO -     AUPRC: 1.0
2025-08-13 02:01:16 - __main__ - INFO -     平衡准确率: 0.9886363636363636
2025-08-13 02:01:16 - __main__ - INFO -     Brier分数: 0.0180431904629584
2025-08-13 02:01:16 - __main__ - INFO -     MCC: 0.9779977967880316
2025-08-13 02:01:16 - __main__ - INFO -   voting_hard:
2025-08-13 02:01:16 - __main__ - INFO -     准确率: 0.9889
2025-08-13 02:01:16 - __main__ - INFO -     F1分数: 0.9889
2025-08-13 02:01:16 - __main__ - INFO -     AUC: 0.0000
2025-08-13 02:01:16 - __main__ - INFO -     AUPRC: 0.0
2025-08-13 02:01:16 - __main__ - INFO -     平衡准确率: 0.9886363636363636
2025-08-13 02:01:16 - __main__ - INFO -     Brier分数: None
2025-08-13 02:01:16 - __main__ - INFO -     MCC: 0.9779977967880316
2025-08-13 02:01:16 - __main__ - INFO - 
验证缓存文件:
2025-08-13 02:01:16 - __main__ - WARNING -   ⚠️ voting_soft 结果缓存文件不存在
2025-08-13 02:01:16 - __main__ - WARNING -   ⚠️ voting_soft 特征名称缓存文件不存在
2025-08-13 02:01:16 - __main__ - WARNING -   ⚠️ voting_hard 结果缓存文件不存在
2025-08-13 02:01:16 - __main__ - WARNING -   ⚠️ voting_hard 特征名称缓存文件不存在
2025-08-13 02:01:16 - __main__ - INFO - 
测试GUI显示格式兼容性:
2025-08-13 02:01:16 - __main__ - INFO -   模拟GUI性能表格显示:
2025-08-13 02:01:16 - __main__ - INFO -   模型名称            准确率      精确率      召回率      F1分数     AUC      AUPRC    Bal_Acc  Brier    MCC     
2025-08-13 02:01:16 - __main__ - INFO -   --------------------------------------------------------------------------------------------------------------
2025-08-13 02:01:16 - __main__ - INFO -   🏆 voting_soft  0.9889   0.9891   0.9889   0.9889   1.0000   1.0000   0.9886   0.0180   0.9780  
2025-08-13 02:01:16 - __main__ - INFO -    2 voting_hard  0.9889   0.9891   0.9889   0.9889   0.0000   0.0000   0.9886   N/A      0.9780  
2025-08-13 02:01:16 - __main__ - INFO -   ✅ GUI显示格式测试通过
2025-08-13 02:01:16 - __main__ - INFO - 
清理测试缓存文件:
2025-08-13 02:02:38 - __main__ - INFO - ================================================================================
2025-08-13 02:02:38 - __main__ - INFO - 测试统一集成结果对象功能
2025-08-13 02:02:38 - __main__ - INFO - ================================================================================
2025-08-13 02:02:38 - __main__ - INFO - 训练集大小: (210, 8)
2025-08-13 02:02:38 - __main__ - INFO - 测试集大小: (90, 8)
2025-08-13 02:02:38 - __main__ - INFO - 开始测试集成学习管道...
2025-08-13 02:02:40 - __main__ - INFO - ✅ 集成学习管道测试成功！
2025-08-13 02:02:40 - __main__ - INFO - 生成了 3 个集成模型
2025-08-13 02:02:40 - __main__ - INFO - 
验证新增指标:
2025-08-13 02:02:40 - __main__ - INFO -   voting_soft:
2025-08-13 02:02:40 - __main__ - INFO -     准确率: 0.9889
2025-08-13 02:02:40 - __main__ - INFO -     F1分数: 0.9889
2025-08-13 02:02:40 - __main__ - INFO -     AUC: 1.0000
2025-08-13 02:02:40 - __main__ - INFO -     AUPRC: 1.0
2025-08-13 02:02:40 - __main__ - INFO -     平衡准确率: 0.9886363636363636
2025-08-13 02:02:40 - __main__ - INFO -     Brier分数: 0.0180431904629584
2025-08-13 02:02:40 - __main__ - INFO -     MCC: 0.9779977967880316
2025-08-13 02:02:40 - __main__ - INFO -   voting_hard:
2025-08-13 02:02:40 - __main__ - INFO -     准确率: 0.9889
2025-08-13 02:02:40 - __main__ - INFO -     F1分数: 0.9889
2025-08-13 02:02:40 - __main__ - INFO -     AUC: 0.0000
2025-08-13 02:02:40 - __main__ - INFO -     AUPRC: 0.0
2025-08-13 02:02:40 - __main__ - INFO -     平衡准确率: 0.9886363636363636
2025-08-13 02:02:40 - __main__ - INFO -     Brier分数: None
2025-08-13 02:02:40 - __main__ - INFO -     MCC: 0.9779977967880316
2025-08-13 02:02:40 - __main__ - INFO -   bagging:
2025-08-13 02:02:40 - __main__ - INFO -     准确率: 0.9667
2025-08-13 02:02:40 - __main__ - INFO -     F1分数: 0.9667
2025-08-13 02:02:40 - __main__ - INFO -     AUC: 0.9960
2025-08-13 02:02:40 - __main__ - INFO -     AUPRC: 0.9957204058885609
2025-08-13 02:02:40 - __main__ - INFO -     平衡准确率: 0.966897233201581
2025-08-13 02:02:40 - __main__ - INFO -     Brier分数: 0.043183577777777774
2025-08-13 02:02:40 - __main__ - INFO -     MCC: 0.9335638713962128
2025-08-13 02:02:40 - __main__ - INFO - 
验证缓存文件:
2025-08-13 02:02:40 - __main__ - INFO -   ✅ voting_soft 结果缓存文件存在
2025-08-13 02:02:40 - __main__ - INFO -     ✅ 缓存结构完整
2025-08-13 02:02:40 - __main__ - INFO -     ✅ 模型类型标记正确: ensemble
2025-08-13 02:02:40 - __main__ - INFO -   ✅ voting_soft 特征名称缓存文件存在
2025-08-13 02:02:40 - __main__ - INFO -   ✅ voting_hard 结果缓存文件存在
2025-08-13 02:02:40 - __main__ - INFO -     ✅ 缓存结构完整
2025-08-13 02:02:40 - __main__ - INFO -     ✅ 模型类型标记正确: ensemble
2025-08-13 02:02:40 - __main__ - INFO -   ✅ voting_hard 特征名称缓存文件存在
2025-08-13 02:02:40 - __main__ - INFO -   ✅ bagging 结果缓存文件存在
2025-08-13 02:02:40 - __main__ - INFO -     ✅ 缓存结构完整
2025-08-13 02:02:40 - __main__ - INFO -     ✅ 模型类型标记正确: ensemble
2025-08-13 02:02:40 - __main__ - INFO -   ✅ bagging 特征名称缓存文件存在
2025-08-13 02:02:40 - __main__ - INFO - 
测试GUI显示格式兼容性:
2025-08-13 02:02:40 - __main__ - INFO -   模拟GUI性能表格显示:
2025-08-13 02:02:40 - __main__ - INFO -   模型名称            准确率      精确率      召回率      F1分数     AUC      AUPRC    Bal_Acc  Brier    MCC     
2025-08-13 02:02:40 - __main__ - INFO -   --------------------------------------------------------------------------------------------------------------
2025-08-13 02:02:40 - __main__ - INFO -   🏆 voting_soft  0.9889   0.9891   0.9889   0.9889   1.0000   1.0000   0.9886   0.0180   0.9780  
2025-08-13 02:02:40 - __main__ - INFO -    2 voting_hard  0.9889   0.9891   0.9889   0.9889   0.0000   0.0000   0.9886   N/A      0.9780  
2025-08-13 02:02:40 - __main__ - INFO -    3 bagging      0.9667   0.9669   0.9667   0.9667   0.9960   0.9957   0.9669   0.0432   0.9336  
2025-08-13 02:02:40 - __main__ - INFO -   ✅ GUI显示格式测试通过
2025-08-13 02:02:40 - __main__ - INFO - 
清理测试缓存文件:
2025-08-13 02:02:40 - __main__ - INFO -   ✅ 已清理: Ensemble_voting_soft_results.joblib
2025-08-13 02:02:40 - __main__ - INFO -   ✅ 已清理: Ensemble_voting_hard_results.joblib
2025-08-13 02:02:40 - __main__ - INFO -   ✅ 已清理: Ensemble_bagging_results.joblib
2025-08-13 08:34:45 - __main__ - INFO - ============================================================
2025-08-13 08:34:45 - __main__ - INFO - 测试GUI导出功能核心逻辑
2025-08-13 08:34:45 - __main__ - INFO - ============================================================
2025-08-13 08:34:45 - __main__ - INFO - 训练集大小: (140, 6)
2025-08-13 08:34:45 - __main__ - INFO - 测试集大小: (60, 6)
2025-08-13 08:34:45 - __main__ - INFO - 开始运行集成学习...
2025-08-13 08:34:46 - __main__ - INFO - ✅ 集成学习成功！
2025-08-13 08:34:46 - __main__ - INFO - 
测试导出逻辑:
2025-08-13 08:34:46 - __main__ - INFO - ✅ CSV导出成功: test_ensemble_results_20250813_083446.csv
2025-08-13 08:34:46 - __main__ - INFO -   CSV包含 2 行数据
2025-08-13 08:34:46 - __main__ - INFO -   列名: ['模型名称', '集成方法', '准确率', '精确率', '召回率', 'F1分数', 'AUC', 'AUPRC', '平衡准确率', 'Brier分数', 'MCC', '投票类型']
2025-08-13 08:34:46 - __main__ - INFO - ✅ JSON导出成功: test_ensemble_results_20250813_083446.json
2025-08-13 08:34:46 - __main__ - INFO -   JSON包含 2 个模型结果
2025-08-13 08:34:46 - __main__ - INFO - 
导出数据示例:
2025-08-13 08:34:46 - __main__ - INFO -   模型 1: voting_soft
2025-08-13 08:34:46 - __main__ - INFO -     准确率: 0.9667
2025-08-13 08:34:46 - __main__ - INFO -     F1分数: 0.9667
2025-08-13 08:34:46 - __main__ - INFO -     AUPRC: 0.9926
2025-08-13 08:34:46 - __main__ - INFO -     平衡准确率: 0.9667
2025-08-13 08:34:46 - __main__ - INFO -     Brier分数: 0.041539550610621104
2025-08-13 08:34:46 - __main__ - INFO -   模型 2: voting_hard
2025-08-13 08:34:46 - __main__ - INFO -     准确率: 0.9500
2025-08-13 08:34:46 - __main__ - INFO -     F1分数: 0.9500
2025-08-13 08:34:46 - __main__ - INFO -     AUPRC: 0.0000
2025-08-13 08:34:46 - __main__ - INFO -     平衡准确率: 0.9500
2025-08-13 08:34:46 - __main__ - INFO -     Brier分数: None
2025-08-13 08:34:46 - __main__ - INFO - 
✅ 测试文件已清理
2025-08-13 08:34:46 - __main__ - INFO - 
测试文件夹路径逻辑:
2025-08-13 08:34:46 - __main__ - INFO - ✅ 结果目录路径: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations
2025-08-13 08:34:46 - __main__ - INFO - ✅ 目录存在: True
2025-08-13 08:34:46 - __main__ - INFO - ✅ 测试文件创建成功: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\visualizations\test_file.txt
2025-08-13 08:34:46 - __main__ - INFO - ✅ 测试文件已清理
2025-08-13 08:50:29 - __main__ - INFO - ================================================================================
2025-08-13 08:50:29 - __main__ - INFO - 测试集成学习超参数调优功能
2025-08-13 08:50:29 - __main__ - INFO - ================================================================================
2025-08-13 08:50:29 - __main__ - INFO - 训练集大小: (280, 10)
2025-08-13 08:50:29 - __main__ - INFO - 测试集大小: (120, 10)
2025-08-13 08:50:29 - __main__ - INFO - 
============================================================
2025-08-13 08:50:29 - __main__ - INFO - 测试1: 不启用调优（基线）
2025-08-13 08:50:29 - __main__ - INFO - ============================================================
2025-08-13 08:50:31 - __main__ - INFO - ✅ 基线测试成功！
2025-08-13 08:50:31 - __main__ - INFO -   voting_soft: F1=0.9250, 准确率=0.9250, AUC=0.9903
2025-08-13 08:50:31 - __main__ - INFO -   voting_hard: F1=0.9416, 准确率=0.9417, AUC=0.0000
2025-08-13 08:50:31 - __main__ - INFO -   bagging: F1=0.9667, 准确率=0.9667, AUC=0.9981
2025-08-13 08:50:31 - __main__ - INFO - 
============================================================
2025-08-13 08:50:31 - __main__ - INFO - 测试2: 启用调优
2025-08-13 08:50:31 - __main__ - INFO - ============================================================
2025-08-13 09:21:27 - __main__ - INFO - ================================================================================
2025-08-13 09:21:27 - __main__ - INFO - 测试GPU vs CPU性能对比
2025-08-13 09:21:27 - __main__ - INFO - ================================================================================
2025-08-13 09:21:27 - __main__ - INFO - 训练集大小: (1400, 20)
2025-08-13 09:21:27 - __main__ - INFO - 测试集大小: (600, 20)
2025-08-13 09:21:27 - __main__ - INFO - 
============================================================
2025-08-13 09:21:27 - __main__ - INFO - 测试1: CPU模式
2025-08-13 09:21:27 - __main__ - INFO - ============================================================
2025-08-13 09:21:45 - __main__ - INFO - ✅ CPU模式测试成功！
2025-08-13 09:21:45 - __main__ - INFO - CPU模式耗时: 18.29秒
2025-08-13 09:21:45 - __main__ - INFO -   voting_soft: F1=0.9667, 准确率=0.9667
2025-08-13 09:21:45 - __main__ - INFO -   voting_hard: F1=0.9700, 准确率=0.9700
2025-08-13 09:21:45 - __main__ - INFO - 
============================================================
2025-08-13 09:21:45 - __main__ - INFO - 测试2: GPU模式
2025-08-13 09:21:45 - __main__ - INFO - ============================================================
2025-08-13 09:22:11 - __main__ - INFO - ✅ GPU模式测试成功！
2025-08-13 09:22:11 - __main__ - INFO - GPU模式耗时: 26.41秒
2025-08-13 09:22:11 - __main__ - INFO -   voting_soft: F1=0.9700, 准确率=0.9700
2025-08-13 09:22:11 - __main__ - INFO -   voting_hard: F1=0.9683, 准确率=0.9683
2025-08-13 09:22:11 - __main__ - INFO - 
🚀 GPU加速比: 0.69x
2025-08-13 09:22:11 - __main__ - WARNING - ❌ GPU模式反而更慢（可能GPU初始化开销）
2025-08-13 09:22:11 - __main__ - INFO - 
================================================================================
2025-08-13 09:22:11 - __main__ - INFO - 测试并行性能对比
2025-08-13 09:22:11 - __main__ - INFO - ================================================================================
2025-08-13 09:22:11 - __main__ - INFO - 
测试1: 单线程模式
