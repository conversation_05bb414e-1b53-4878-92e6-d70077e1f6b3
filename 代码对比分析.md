# Multi Model 01 vs 02 代码对比分析

## 关键发现：为什么原版流畅而更新版卡住

### 1. LightGBM Objective函数对比

#### Multi Model 01 (原版 - 工作正常)
```python
def objective_lightgbm(trial, X_train, y_train, scoring='roc_auc'):
    params = {
        "n_estimators": trial.suggest_int("n_estimators", 50, 300),
        "max_depth": trial.suggest_int("max_depth", 2, 10),
        "learning_rate": trial.suggest_float("learning_rate", 0.01, 0.3),
        "feature_fraction": trial.suggest_float("feature_fraction", 0.5, 1.0),
        "bagging_fraction": trial.suggest_float("bagging_fraction", 0.5, 1.0),
    }
    
    # 简单的GPU配置
    if USE_GPU and GPU_INFO.get('lightgbm_gpu', False):
        params["device"] = "gpu"
        params["gpu_platform_id"] = 0
        params["gpu_device_id"] = GPU_IDS[0]
    else:
        params["device"] = "cpu"
    
    params["verbose"] = -1
    
    model = LGBMClassifier(**params, random_state=RANDOM_SEED)
    cv = _build_cv(y_train, n_splits=5, random_state=RANDOM_SEED)
    return cross_val_score(model, X_train, y_train, cv=cv, scoring=scoring).mean()
```

#### Multi Model 02 (更新版 - 原始问题版本)
```python
def objective_lightgbm(trial, X_train, y_train, scoring='roc_auc'):
    params = {
        "n_estimators": trial.suggest_int("n_estimators", 50, 200),  # 减少上限避免过度训练
        "max_depth": trial.suggest_int("max_depth", 3, 8),  # 限制深度避免过拟合
        "learning_rate": trial.suggest_float("learning_rate", 0.05, 0.2),  # 更保守的学习率范围
        "feature_fraction": trial.suggest_float("feature_fraction", 0.6, 0.9),  # 更保守的特征采样
        "bagging_fraction": trial.suggest_float("bagging_fraction", 0.6, 0.9),  # 更保守的样本采样
        "min_child_samples": trial.suggest_int("min_child_samples", 10, 50),  # 添加最小样本数限制
        "reg_alpha": trial.suggest_float("reg_alpha", 0.0, 1.0),  # L1正则化
        "reg_lambda": trial.suggest_float("reg_lambda", 0.0, 1.0),  # L2正则化
    }

    # 复杂的配置和错误的参数
    params["device"] = "cpu"
    params["verbose"] = -1
    params["force_row_wise"] = True
    params["deterministic"] = True
    params["early_stopping_rounds"] = 10  # ❌ 错误！这个参数不适用于cross_val_score
    params["eval_metric"] = "auc"  # ❌ 错误！这个参数也不适用

    # ❌ 复杂的线程超时机制导致卡住
    import threading
    result = [None]
    exception = [None]
    
    def run_cv():
        try:
            result[0] = cross_val_score(model, X_train, y_train, cv=cv, scoring=scoring).mean()
        except Exception as e:
            exception[0] = e

    thread = threading.Thread(target=run_cv)
    thread.daemon = True
    thread.start()
    thread.join(timeout=30)  # 30秒超时
    
    if thread.is_alive():
        return 0.5  # 返回低分
```

### 2. 核心问题分析

#### 原版为什么流畅运行：
1. **简单直接**: 没有复杂的线程机制
2. **参数正确**: 只使用LightGBM支持的参数
3. **无超时机制**: 让模型自然完成训练
4. **配置简单**: GPU配置逻辑简单明了

#### 更新版为什么卡住：
1. **错误参数**: early_stopping_rounds和eval_metric不适用于cross_val_score
2. **复杂线程**: 线程超时机制可能导致死锁
3. **过度配置**: 添加了太多"优化"参数实际上降低了性能
4. **资源竞争**: 复杂的并行配置导致资源冲突

### 3. KNN对比

#### 原版 (简单有效)
```python
def objective_knn(trial, X_train, y_train, scoring='roc_auc'):
    params = {
        "clf__n_neighbors": trial.suggest_int("clf__n_neighbors", 3, 10),
    }
    pipe = Pipeline([("scaler", StandardScaler()), ("clf", KNeighborsClassifier())])
    pipe.set_params(**params)
    cv = _build_cv(y_train, n_splits=5, random_state=RANDOM_SEED)
    return cross_val_score(pipe, X_train, y_train, cv=cv, scoring=scoring).mean()
```

#### 更新版 (过度复杂)
- 添加了weights, algorithm, metric等多个参数
- 添加了复杂的线程超时机制
- 动态调整参数范围
- 结果：搜索空间过大，性能下降

### 4. 并行处理对比

#### 原版 (简单有效)
```python
# 只对CatBoost特殊处理
actual_n_jobs = 1 if model_name == "CatBoost" and USE_GPU else (n_jobs if n_jobs > 1 else 1)
```

#### 更新版 (过度复杂)
```python
# 为每个模型都有复杂的并行配置逻辑
if model_name in ["LightGBM", "XGBoost"]:
    actual_n_jobs = 1
elif model_name == "CatBoost":
    actual_n_jobs = 1
elif model_name == "KNN":
    actual_n_jobs = min(4, max(1, n_jobs // 2))
# ... 更多复杂逻辑
```

## 根本原因总结

**原版成功的关键**：
- 遵循"简单即美"原则
- 只使用经过验证的参数
- 避免过度工程化
- 让模型库自己处理优化

**更新版失败的原因**：
- 过度工程化，添加了太多"优化"
- 使用了不适用的参数组合
- 复杂的线程机制引入了新的问题
- 试图"改进"已经工作良好的代码

## 修复策略

应该回到原版的简单实现，只保留必要的改进，避免过度复杂化。
