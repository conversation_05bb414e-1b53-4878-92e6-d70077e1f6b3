#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的模型推理模块
提供统一的模型加载、预测和推理功能
"""

import os
import numpy as np
import pandas as pd
from pathlib import Path
from joblib import load, dump
import pickle
import json
from typing import Union, Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

from config import CACHE_PATH, OUTPUT_PATH, MODEL_NAMES
from logger import get_logger
from data_preprocessing import DataPreprocessor

logger = get_logger("model_inference")


class ModelInference:
    """
    模型推理类
    提供统一的模型加载、预测和推理接口
    """
    
    def __init__(self):
        """初始化推理器"""
        self.loaded_models = {}  # 缓存已加载的模型
        self.data_preprocessor = DataPreprocessor()
        self.model_metadata = {}  # 存储模型元数据
        
    def load_model(self, model_name: str, force_reload: bool = False) -> bool:
        """
        加载指定的模型
        
        Args:
            model_name: 模型名称
            force_reload: 是否强制重新加载
            
        Returns:
            bool: 加载是否成功
        """
        if model_name in self.loaded_models and not force_reload:
            logger.info(f"模型 {model_name} 已在缓存中")
            return True
            
        cache_file = CACHE_PATH / f"{model_name}_results.joblib"
        if not cache_file.exists():
            logger.error(f"模型缓存文件不存在: {cache_file}")
            return False
            
        try:
            model_data = load(cache_file)
            self.loaded_models[model_name] = model_data
            
            # 提取模型元数据
            self.model_metadata[model_name] = {
                'model_type': type(model_data['model']).__name__,
                'feature_names': model_data.get('feature_names', None),
                'scaler': model_data.get('scaler', None),
                'data_path': model_data.get('data_path', None),
                'training_time': model_data.get('training_time', None),
                'performance_metrics': model_data.get('performance_metrics', {})
            }
            
            logger.info(f"成功加载模型 {model_name}")
            return True
            
        except Exception as e:
            logger.error(f"加载模型 {model_name} 失败: {e}")
            return False
    
    def load_all_available_models(self) -> Dict[str, bool]:
        """
        加载所有可用的模型
        
        Returns:
            Dict[str, bool]: 每个模型的加载状态
        """
        results = {}
        for model_name in MODEL_NAMES:
            results[model_name] = self.load_model(model_name)
        
        loaded_count = sum(results.values())
        logger.info(f"成功加载 {loaded_count}/{len(MODEL_NAMES)} 个模型")
        return results
    
    def get_loaded_models(self) -> List[str]:
        """获取已加载的模型列表"""
        return list(self.loaded_models.keys())
    
    def get_model_info(self, model_name: str) -> Optional[Dict]:
        """
        获取模型信息
        
        Args:
            model_name: 模型名称
            
        Returns:
            Dict: 模型信息，如果模型未加载则返回None
        """
        if model_name not in self.loaded_models:
            logger.warning(f"模型 {model_name} 未加载")
            return None
            
        return self.model_metadata.get(model_name, {})
    
    def predict(self, model_name: str, X: Union[np.ndarray, pd.DataFrame], 
                return_proba: bool = True) -> Optional[np.ndarray]:
        """
        使用指定模型进行预测
        
        Args:
            model_name: 模型名称
            X: 输入特征数据
            return_proba: 是否返回预测概率
            
        Returns:
            np.ndarray: 预测结果，失败时返回None
        """
        if model_name not in self.loaded_models:
            logger.error(f"模型 {model_name} 未加载，请先调用 load_model()")
            return None
            
        try:
            model_data = self.loaded_models[model_name]
            model = model_data['model']
            scaler = model_data.get('scaler', None)
            
            # 数据预处理
            X_processed = self._preprocess_input(X, scaler)
            
            # 进行预测
            if return_proba and hasattr(model, 'predict_proba'):
                predictions = model.predict_proba(X_processed)
                # 对于二分类，返回正类概率
                if predictions.shape[1] == 2:
                    predictions = predictions[:, 1]
            elif hasattr(model, 'decision_function') and return_proba:
                predictions = model.decision_function(X_processed)
            else:
                predictions = model.predict(X_processed)
                
            logger.info(f"使用模型 {model_name} 完成 {len(X_processed)} 个样本的预测")
            return predictions
            
        except Exception as e:
            logger.error(f"模型 {model_name} 预测失败: {e}")
            return None
    
    def predict_batch(self, model_names: List[str], X: Union[np.ndarray, pd.DataFrame],
                     return_proba: bool = True) -> Dict[str, np.ndarray]:
        """
        使用多个模型进行批量预测
        
        Args:
            model_names: 模型名称列表
            X: 输入特征数据
            return_proba: 是否返回预测概率
            
        Returns:
            Dict[str, np.ndarray]: 每个模型的预测结果
        """
        results = {}
        for model_name in model_names:
            predictions = self.predict(model_name, X, return_proba)
            if predictions is not None:
                results[model_name] = predictions
                
        logger.info(f"完成 {len(results)}/{len(model_names)} 个模型的批量预测")
        return results
    
    def ensemble_predict(self, model_names: List[str], X: Union[np.ndarray, pd.DataFrame],
                        method: str = 'average', weights: Optional[List[float]] = None) -> Optional[np.ndarray]:
        """
        集成预测
        
        Args:
            model_names: 参与集成的模型名称列表
            X: 输入特征数据
            method: 集成方法 ('average', 'weighted', 'voting')
            weights: 模型权重（仅在method='weighted'时使用）
            
        Returns:
            np.ndarray: 集成预测结果
        """
        # 获取所有模型的预测结果
        predictions = self.predict_batch(model_names, X, return_proba=True)
        
        if not predictions:
            logger.error("没有成功的预测结果")
            return None
            
        # 转换为数组
        pred_arrays = list(predictions.values())
        
        if method == 'average':
            ensemble_pred = np.mean(pred_arrays, axis=0)
        elif method == 'weighted':
            if weights is None:
                weights = [1.0] * len(pred_arrays)
            elif len(weights) != len(pred_arrays):
                logger.error("权重数量与模型数量不匹配")
                return None
            ensemble_pred = np.average(pred_arrays, axis=0, weights=weights)
        elif method == 'voting':
            # 硬投票：将概率转换为类别后投票
            binary_preds = [pred > 0.5 for pred in pred_arrays]
            ensemble_pred = np.mean(binary_preds, axis=0)
        else:
            logger.error(f"不支持的集成方法: {method}")
            return None
            
        logger.info(f"完成 {len(model_names)} 个模型的集成预测")
        return ensemble_pred
    
    def _preprocess_input(self, X: Union[np.ndarray, pd.DataFrame], 
                         scaler: Optional[Any] = None) -> np.ndarray:
        """
        预处理输入数据
        
        Args:
            X: 输入数据
            scaler: 数据缩放器
            
        Returns:
            np.ndarray: 预处理后的数据
        """
        # 确保数据格式正确
        if isinstance(X, pd.DataFrame):
            X_processed = X.values
        else:
            X_processed = np.array(X)
            
        # 应用缩放器
        if scaler is not None:
            try:
                X_processed = scaler.transform(X_processed)
                logger.debug("应用数据缩放器")
            except Exception as e:
                logger.warning(f"应用缩放器失败: {e}，使用原始数据")
                
        return X_processed
    
    def save_predictions(self, predictions: Dict[str, np.ndarray], 
                        output_file: str, include_metadata: bool = True) -> bool:
        """
        保存预测结果
        
        Args:
            predictions: 预测结果字典
            output_file: 输出文件路径
            include_metadata: 是否包含元数据
            
        Returns:
            bool: 保存是否成功
        """
        try:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 准备保存的数据
            save_data = {
                'predictions': predictions,
                'timestamp': pd.Timestamp.now().isoformat()
            }
            
            if include_metadata:
                save_data['model_metadata'] = self.model_metadata
                save_data['loaded_models'] = list(self.loaded_models.keys())
            
            # 根据文件扩展名选择保存格式
            if output_path.suffix.lower() == '.json':
                # 转换numpy数组为列表以便JSON序列化
                json_data = save_data.copy()
                json_data['predictions'] = {k: v.tolist() for k, v in predictions.items()}
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(json_data, f, indent=2, ensure_ascii=False)
            else:
                # 使用joblib保存
                dump(save_data, output_path)
                
            logger.info(f"预测结果已保存至: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存预测结果失败: {e}")
            return False
    
    def clear_cache(self):
        """清除模型缓存"""
        self.loaded_models.clear()
        self.model_metadata.clear()
        logger.info("模型缓存已清除")


# 便捷函数
def quick_predict(model_name: str, X: Union[np.ndarray, pd.DataFrame], 
                 return_proba: bool = True) -> Optional[np.ndarray]:
    """
    快速预测函数
    
    Args:
        model_name: 模型名称
        X: 输入特征数据
        return_proba: 是否返回预测概率
        
    Returns:
        np.ndarray: 预测结果
    """
    inference = ModelInference()
    if inference.load_model(model_name):
        return inference.predict(model_name, X, return_proba)
    return None


def quick_ensemble_predict(model_names: List[str], X: Union[np.ndarray, pd.DataFrame],
                          method: str = 'average') -> Optional[np.ndarray]:
    """
    快速集成预测函数
    
    Args:
        model_names: 模型名称列表
        X: 输入特征数据
        method: 集成方法
        
    Returns:
        np.ndarray: 集成预测结果
    """
    inference = ModelInference()
    # 加载所有需要的模型
    for model_name in model_names:
        inference.load_model(model_name)
    
    return inference.ensemble_predict(model_names, X, method)


if __name__ == "__main__":
    # 示例用法
    print("模型推理模块示例")
    
    # 创建推理器
    inference = ModelInference()
    
    # 加载所有可用模型
    load_results = inference.load_all_available_models()
    print(f"模型加载结果: {load_results}")
    
    # 显示已加载的模型
    loaded_models = inference.get_loaded_models()
    print(f"已加载的模型: {loaded_models}")
