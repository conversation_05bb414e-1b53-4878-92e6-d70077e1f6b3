2025-08-13 08:50:29 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-13 08:50:29 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-13 08:50:29 - __main__ - INFO - ================================================================================
2025-08-13 08:50:29 - __main__ - INFO - 测试集成学习超参数调优功能
2025-08-13 08:50:29 - __main__ - INFO - ================================================================================
2025-08-13 08:50:29 - __main__ - INFO - 训练集大小: (280, 10)
2025-08-13 08:50:29 - __main__ - INFO - 测试集大小: (120, 10)
2025-08-13 08:50:29 - __main__ - INFO - 
============================================================
2025-08-13 08:50:29 - __main__ - INFO - 测试1: 不启用调优（基线）
2025-08-13 08:50:29 - __main__ - INFO - ============================================================
2025-08-13 08:50:29 - model_ensemble - INFO - ============================================================
2025-08-13 08:50:29 - model_ensemble - INFO - 开始运行集成学习管道
2025-08-13 08:50:29 - model_ensemble - INFO - ============================================================
2025-08-13 08:50:29 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-08-13 08:50:29 - model_ensemble - INFO - 集成方法: ['voting', 'bagging']
2025-08-13 08:50:29 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-08-13 08:50:29 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-08-13 08:50:29 - model_training - INFO - 模型名称: Random Forest
2025-08-13 08:50:29 - model_training - INFO - 准确率: 0.9583
2025-08-13 08:50:29 - model_training - INFO - AUC: 0.9974
2025-08-13 08:50:29 - model_training - INFO - AUPRC: 0.9973
2025-08-13 08:50:29 - model_training - INFO - 混淆矩阵:
2025-08-13 08:50:29 - model_training - INFO - 
[[59  1]
 [ 4 56]]
2025-08-13 08:50:29 - model_training - INFO - 
分类报告:
2025-08-13 08:50:29 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.98      0.96        60
           1       0.98      0.93      0.96        60

    accuracy                           0.96       120
   macro avg       0.96      0.96      0.96       120
weighted avg       0.96      0.96      0.96       120

2025-08-13 08:50:29 - model_training - INFO - 训练时间: 0.12 秒
2025-08-13 08:50:29 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9583
2025-08-13 08:50:29 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-13 08:50:29 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-13 08:50:29 - model_ensemble - INFO -   RandomForest 训练完成
2025-08-13 08:50:29 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-08-13 08:50:29 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.000 以处理不平衡
2025-08-13 08:50:29 - model_training - INFO - 模型名称: XGBoost
2025-08-13 08:50:29 - model_training - INFO - 准确率: 0.9083
2025-08-13 08:50:29 - model_training - INFO - AUC: 0.9872
2025-08-13 08:50:29 - model_training - INFO - AUPRC: 0.9880
2025-08-13 08:50:29 - model_training - INFO - 混淆矩阵:
2025-08-13 08:50:29 - model_training - INFO - 
[[55  5]
 [ 6 54]]
2025-08-13 08:50:29 - model_training - INFO - 
分类报告:
2025-08-13 08:50:29 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.92      0.91        60
           1       0.92      0.90      0.91        60

    accuracy                           0.91       120
   macro avg       0.91      0.91      0.91       120
weighted avg       0.91      0.91      0.91       120

2025-08-13 08:50:29 - model_training - INFO - 训练时间: 0.11 秒
2025-08-13 08:50:30 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9083
2025-08-13 08:50:30 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-13 08:50:30 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-13 08:50:30 - model_ensemble - INFO -   XGBoost 训练完成
2025-08-13 08:50:30 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-08-13 08:50:30 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-08-13 08:50:30 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-08-13 08:50:30 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 08:50:30 - model_ensemble - INFO - 集成模型 voting_soft 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_soft_results.joblib
2025-08-13 08:50:30 - model_ensemble - INFO - 集成模型 voting_soft 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_soft_feature_names.joblib
2025-08-13 08:50:30 - model_ensemble - INFO -     voting_soft - 准确率: 0.9250, F1: 0.9250
2025-08-13 08:50:30 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-08-13 08:50:30 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 08:50:30 - model_ensemble - INFO - 集成模型 voting_hard 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_hard_results.joblib
2025-08-13 08:50:30 - model_ensemble - INFO - 集成模型 voting_hard 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_hard_feature_names.joblib
2025-08-13 08:50:30 - model_ensemble - INFO -     voting_hard - 准确率: 0.9417, F1: 0.9416
2025-08-13 08:50:30 - model_ensemble - INFO - 步骤2: 运行集成方法 - bagging
2025-08-13 08:50:30 - model_ensemble - INFO - 开始训练集成模型，方法: bagging
2025-08-13 08:50:31 - model_ensemble - INFO - 集成模型 bagging 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_bagging_results.joblib
2025-08-13 08:50:31 - model_ensemble - INFO - 集成模型 bagging 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_bagging_feature_names.joblib
2025-08-13 08:50:31 - model_ensemble - INFO -   bagging - 准确率: 0.9667, F1: 0.9667
2025-08-13 08:50:31 - model_ensemble - INFO - ============================================================
2025-08-13 08:50:31 - model_ensemble - INFO - 集成学习结果总结
2025-08-13 08:50:31 - model_ensemble - INFO - ============================================================
2025-08-13 08:50:31 - model_ensemble - INFO - 最佳集成模型: bagging
2025-08-13 08:50:31 - model_ensemble - INFO - 最佳F1分数: 0.9667
2025-08-13 08:50:31 - model_ensemble - INFO - 最佳准确率: 0.9667
2025-08-13 08:50:31 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-08-13 08:50:31 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9250, 精确率: 0.9251, 召回率: 0.9250, F1: 0.9250, AUC: 0.9903
2025-08-13 08:50:31 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9417, 精确率: 0.9448, 召回率: 0.9417, F1: 0.9416, AUC: 0.0000
2025-08-13 08:50:31 - model_ensemble - INFO -   bagging         - 准确率: 0.9667, 精确率: 0.9672, 召回率: 0.9667, F1: 0.9667, AUC: 0.9981
2025-08-13 08:50:31 - __main__ - INFO - ✅ 基线测试成功！
2025-08-13 08:50:31 - __main__ - INFO -   voting_soft: F1=0.9250, 准确率=0.9250, AUC=0.9903
2025-08-13 08:50:31 - __main__ - INFO -   voting_hard: F1=0.9416, 准确率=0.9417, AUC=0.0000
2025-08-13 08:50:31 - __main__ - INFO -   bagging: F1=0.9667, 准确率=0.9667, AUC=0.9981
2025-08-13 08:50:31 - __main__ - INFO - 
============================================================
2025-08-13 08:50:31 - __main__ - INFO - 测试2: 启用调优
2025-08-13 08:50:31 - __main__ - INFO - ============================================================
2025-08-13 08:50:31 - model_ensemble - INFO - ============================================================
2025-08-13 08:50:31 - model_ensemble - INFO - 开始运行集成学习管道
2025-08-13 08:50:31 - model_ensemble - INFO - ============================================================
2025-08-13 08:50:31 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-08-13 08:50:31 - model_ensemble - INFO - 集成方法: ['voting', 'bagging']
2025-08-13 08:50:31 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-08-13 08:50:31 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-08-13 08:50:31 - model_training - INFO - 模型名称: Random Forest
2025-08-13 08:50:31 - model_training - INFO - 准确率: 0.9583
2025-08-13 08:50:31 - model_training - INFO - AUC: 0.9974
2025-08-13 08:50:31 - model_training - INFO - AUPRC: 0.9973
2025-08-13 08:50:31 - model_training - INFO - 混淆矩阵:
2025-08-13 08:50:31 - model_training - INFO - 
[[59  1]
 [ 4 56]]
2025-08-13 08:50:31 - model_training - INFO - 
分类报告:
2025-08-13 08:50:31 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.98      0.96        60
           1       0.98      0.93      0.96        60

    accuracy                           0.96       120
   macro avg       0.96      0.96      0.96       120
weighted avg       0.96      0.96      0.96       120

2025-08-13 08:50:31 - model_training - INFO - 训练时间: 0.13 秒
2025-08-13 08:50:31 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9583
2025-08-13 08:50:31 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-13 08:50:31 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-13 08:50:31 - model_ensemble - INFO -   RandomForest 训练完成
2025-08-13 08:50:31 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-08-13 08:50:31 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.000 以处理不平衡
2025-08-13 08:50:31 - model_training - INFO - 模型名称: XGBoost
2025-08-13 08:50:31 - model_training - INFO - 准确率: 0.9083
2025-08-13 08:50:31 - model_training - INFO - AUC: 0.9872
2025-08-13 08:50:31 - model_training - INFO - AUPRC: 0.9880
2025-08-13 08:50:31 - model_training - INFO - 混淆矩阵:
2025-08-13 08:50:31 - model_training - INFO - 
[[55  5]
 [ 6 54]]
2025-08-13 08:50:31 - model_training - INFO - 
分类报告:
2025-08-13 08:50:31 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.92      0.91        60
           1       0.92      0.90      0.91        60

    accuracy                           0.91       120
   macro avg       0.91      0.91      0.91       120
weighted avg       0.91      0.91      0.91       120

2025-08-13 08:50:31 - model_training - INFO - 训练时间: 0.03 秒
2025-08-13 08:50:31 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9083
2025-08-13 08:50:31 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-13 08:50:31 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-13 08:50:31 - model_ensemble - INFO -   XGBoost 训练完成
2025-08-13 08:50:31 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-08-13 08:50:31 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-08-13 08:50:31 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-08-13 08:50:31 - model_ensemble - INFO -     正在优化 voting_soft 的权重...
2025-08-13 08:50:46 - model_ensemble - INFO - Voting权重优化完成: 最优CV f1_weighted=0.9857
2025-08-13 08:50:46 - model_ensemble - INFO -     使用优化权重: [0.87336527 0.12663473]
2025-08-13 08:50:46 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 08:50:46 - model_ensemble - INFO - 集成模型 voting_soft 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_soft_results.joblib
2025-08-13 08:50:46 - model_ensemble - INFO - 集成模型 voting_soft 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_soft_feature_names.joblib
2025-08-13 08:50:46 - model_ensemble - INFO -     voting_soft - 准确率: 0.9583, F1: 0.9583
2025-08-13 08:50:46 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-08-13 08:50:46 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 08:50:46 - model_ensemble - INFO - 集成模型 voting_hard 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_hard_results.joblib
2025-08-13 08:50:46 - model_ensemble - INFO - 集成模型 voting_hard 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_hard_feature_names.joblib
2025-08-13 08:50:46 - model_ensemble - INFO -     voting_hard - 准确率: 0.9417, F1: 0.9416
2025-08-13 08:50:46 - model_ensemble - INFO - 步骤2: 运行集成方法 - bagging
2025-08-13 08:50:46 - model_ensemble - INFO -     正在优化 bagging 的参数...
