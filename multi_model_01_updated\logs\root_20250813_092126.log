2025-08-13 09:21:26 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-13 09:21:27 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-13 09:21:27 - __main__ - INFO - ================================================================================
2025-08-13 09:21:27 - __main__ - INFO - 测试GPU vs CPU性能对比
2025-08-13 09:21:27 - __main__ - INFO - ================================================================================
2025-08-13 09:21:27 - __main__ - INFO - 训练集大小: (1400, 20)
2025-08-13 09:21:27 - __main__ - INFO - 测试集大小: (600, 20)
2025-08-13 09:21:27 - __main__ - INFO - 
============================================================
2025-08-13 09:21:27 - __main__ - INFO - 测试1: CPU模式
2025-08-13 09:21:27 - __main__ - INFO - ============================================================
2025-08-13 09:21:27 - model_ensemble - INFO - ============================================================
2025-08-13 09:21:27 - model_ensemble - INFO - 开始运行集成学习管道
2025-08-13 09:21:27 - model_ensemble - INFO - ============================================================
2025-08-13 09:21:27 - model_ensemble - INFO - 基础模型: ['XGBoost', 'RandomForest']
2025-08-13 09:21:27 - model_ensemble - INFO - 集成方法: ['voting']
2025-08-13 09:21:27 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-08-13 09:21:27 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-08-13 09:21:27 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.000 以处理不平衡
2025-08-13 09:21:27 - model_training - INFO - 模型名称: XGBoost
2025-08-13 09:21:27 - model_training - INFO - 准确率: 0.9700
2025-08-13 09:21:27 - model_training - INFO - AUC: 0.9902
2025-08-13 09:21:27 - model_training - INFO - AUPRC: 0.9912
2025-08-13 09:21:27 - model_training - INFO - 混淆矩阵:
2025-08-13 09:21:27 - model_training - INFO - 
[[294   5]
 [ 13 288]]
2025-08-13 09:21:27 - model_training - INFO - 
分类报告:
2025-08-13 09:21:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.96      0.98      0.97       299
           1       0.98      0.96      0.97       301

    accuracy                           0.97       600
   macro avg       0.97      0.97      0.97       600
weighted avg       0.97      0.97      0.97       600

2025-08-13 09:21:27 - model_training - INFO - 训练时间: 0.15 秒
2025-08-13 09:21:27 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9700
2025-08-13 09:21:27 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-13 09:21:27 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-13 09:21:27 - model_ensemble - INFO -   XGBoost 训练完成
2025-08-13 09:21:27 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-08-13 09:21:27 - model_training - INFO - 模型名称: Random Forest
2025-08-13 09:21:27 - model_training - INFO - 准确率: 0.9683
2025-08-13 09:21:27 - model_training - INFO - AUC: 0.9879
2025-08-13 09:21:27 - model_training - INFO - AUPRC: 0.9852
2025-08-13 09:21:27 - model_training - INFO - 混淆矩阵:
2025-08-13 09:21:27 - model_training - INFO - 
[[290   9]
 [ 10 291]]
2025-08-13 09:21:27 - model_training - INFO - 
分类报告:
2025-08-13 09:21:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.97      0.97      0.97       299
           1       0.97      0.97      0.97       301

    accuracy                           0.97       600
   macro avg       0.97      0.97      0.97       600
weighted avg       0.97      0.97      0.97       600

2025-08-13 09:21:27 - model_training - INFO - 训练时间: 0.35 秒
2025-08-13 09:21:27 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9683
2025-08-13 09:21:27 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-13 09:21:27 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-13 09:21:27 - model_ensemble - INFO -   RandomForest 训练完成
2025-08-13 09:21:27 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-08-13 09:21:27 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-08-13 09:21:27 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-08-13 09:21:27 - model_ensemble - INFO -     正在优化 voting_soft 的权重...
2025-08-13 09:21:44 - model_ensemble - INFO - Voting权重优化完成: 最优CV f1_weighted=0.9671
2025-08-13 09:21:44 - model_ensemble - INFO -     使用优化权重: [0.38499304 0.61500696]
2025-08-13 09:21:44 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 09:21:44 - model_ensemble - INFO - 集成模型 voting_soft 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_soft_results.joblib
2025-08-13 09:21:44 - model_ensemble - INFO - 集成模型 voting_soft 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_soft_feature_names.joblib
2025-08-13 09:21:44 - model_ensemble - INFO -     voting_soft - 准确率: 0.9667, F1: 0.9667
2025-08-13 09:21:44 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-08-13 09:21:44 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 09:21:45 - model_ensemble - INFO - 集成模型 voting_hard 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_hard_results.joblib
2025-08-13 09:21:45 - model_ensemble - INFO - 集成模型 voting_hard 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_hard_feature_names.joblib
2025-08-13 09:21:45 - model_ensemble - INFO -     voting_hard - 准确率: 0.9700, F1: 0.9700
2025-08-13 09:21:45 - model_ensemble - INFO - ============================================================
2025-08-13 09:21:45 - model_ensemble - INFO - 集成学习结果总结
2025-08-13 09:21:45 - model_ensemble - INFO - ============================================================
2025-08-13 09:21:45 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-08-13 09:21:45 - model_ensemble - INFO - 最佳F1分数: 0.9700
2025-08-13 09:21:45 - model_ensemble - INFO - 最佳准确率: 0.9700
2025-08-13 09:21:45 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-08-13 09:21:45 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9667, 精确率: 0.9668, 召回率: 0.9667, F1: 0.9667, AUC: 0.9889
2025-08-13 09:21:45 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9700, 精确率: 0.9705, 召回率: 0.9700, F1: 0.9700, AUC: 0.0000
2025-08-13 09:21:45 - __main__ - INFO - ✅ CPU模式测试成功！
2025-08-13 09:21:45 - __main__ - INFO - CPU模式耗时: 18.29秒
2025-08-13 09:21:45 - __main__ - INFO -   voting_soft: F1=0.9667, 准确率=0.9667
2025-08-13 09:21:45 - __main__ - INFO -   voting_hard: F1=0.9700, 准确率=0.9700
2025-08-13 09:21:45 - __main__ - INFO - 
============================================================
2025-08-13 09:21:45 - __main__ - INFO - 测试2: GPU模式
2025-08-13 09:21:45 - __main__ - INFO - ============================================================
2025-08-13 09:21:45 - model_ensemble - INFO - ============================================================
2025-08-13 09:21:45 - model_ensemble - INFO - 开始运行集成学习管道
2025-08-13 09:21:45 - model_ensemble - INFO - ============================================================
2025-08-13 09:21:45 - model_ensemble - INFO - 基础模型: ['XGBoost', 'RandomForest']
2025-08-13 09:21:45 - model_ensemble - INFO - 集成方法: ['voting']
2025-08-13 09:21:45 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-08-13 09:21:45 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-08-13 09:21:46 - model_training - INFO - [XGBoost] 启用GPU加速
2025-08-13 09:21:46 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.000 以处理不平衡
2025-08-13 09:21:47 - model_training - INFO - 模型名称: XGBoost
2025-08-13 09:21:47 - model_training - INFO - 准确率: 0.9683
2025-08-13 09:21:47 - model_training - INFO - AUC: 0.9899
2025-08-13 09:21:47 - model_training - INFO - AUPRC: 0.9896
2025-08-13 09:21:47 - model_training - INFO - 混淆矩阵:
2025-08-13 09:21:47 - model_training - INFO - 
[[292   7]
 [ 12 289]]
2025-08-13 09:21:47 - model_training - INFO - 
分类报告:
2025-08-13 09:21:47 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.96      0.98      0.97       299
           1       0.98      0.96      0.97       301

    accuracy                           0.97       600
   macro avg       0.97      0.97      0.97       600
weighted avg       0.97      0.97      0.97       600

2025-08-13 09:21:47 - model_training - INFO - 训练时间: 1.79 秒
2025-08-13 09:21:47 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9683
2025-08-13 09:21:47 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-13 09:21:47 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-13 09:21:47 - model_ensemble - INFO -   XGBoost 训练完成
2025-08-13 09:21:47 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-08-13 09:21:47 - model_training - INFO - 模型名称: Random Forest
2025-08-13 09:21:47 - model_training - INFO - 准确率: 0.9683
2025-08-13 09:21:47 - model_training - INFO - AUC: 0.9879
2025-08-13 09:21:47 - model_training - INFO - AUPRC: 0.9852
2025-08-13 09:21:47 - model_training - INFO - 混淆矩阵:
2025-08-13 09:21:47 - model_training - INFO - 
[[290   9]
 [ 10 291]]
2025-08-13 09:21:47 - model_training - INFO - 
分类报告:
2025-08-13 09:21:47 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.97      0.97      0.97       299
           1       0.97      0.97      0.97       301

    accuracy                           0.97       600
   macro avg       0.97      0.97      0.97       600
weighted avg       0.97      0.97      0.97       600

2025-08-13 09:21:47 - model_training - INFO - 训练时间: 0.35 秒
2025-08-13 09:21:47 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9683
2025-08-13 09:21:47 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-13 09:21:47 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-13 09:21:47 - model_ensemble - INFO -   RandomForest 训练完成
2025-08-13 09:21:47 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-08-13 09:21:47 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-08-13 09:21:47 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-08-13 09:21:47 - model_ensemble - INFO -     正在优化 voting_soft 的权重...
2025-08-13 09:22:10 - model_ensemble - INFO - Voting权重优化完成: 最优CV f1_weighted=0.9700
2025-08-13 09:22:10 - model_ensemble - INFO -     使用优化权重: [0.36899461 0.63100539]
2025-08-13 09:22:10 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 09:22:11 - model_ensemble - INFO - 集成模型 voting_soft 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_soft_results.joblib
2025-08-13 09:22:11 - model_ensemble - INFO - 集成模型 voting_soft 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_soft_feature_names.joblib
2025-08-13 09:22:11 - model_ensemble - INFO -     voting_soft - 准确率: 0.9700, F1: 0.9700
2025-08-13 09:22:11 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-08-13 09:22:11 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 09:22:11 - model_ensemble - INFO - 集成模型 voting_hard 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_hard_results.joblib
2025-08-13 09:22:11 - model_ensemble - INFO - 集成模型 voting_hard 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_hard_feature_names.joblib
2025-08-13 09:22:11 - model_ensemble - INFO -     voting_hard - 准确率: 0.9683, F1: 0.9683
2025-08-13 09:22:11 - model_ensemble - INFO - ============================================================
2025-08-13 09:22:11 - model_ensemble - INFO - 集成学习结果总结
2025-08-13 09:22:11 - model_ensemble - INFO - ============================================================
2025-08-13 09:22:11 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-08-13 09:22:11 - model_ensemble - INFO - 最佳F1分数: 0.9700
2025-08-13 09:22:11 - model_ensemble - INFO - 最佳准确率: 0.9700
2025-08-13 09:22:11 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-08-13 09:22:11 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9700, 精确率: 0.9701, 召回率: 0.9700, F1: 0.9700, AUC: 0.9884
2025-08-13 09:22:11 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9683, 精确率: 0.9686, 召回率: 0.9683, F1: 0.9683, AUC: 0.0000
2025-08-13 09:22:11 - __main__ - INFO - ✅ GPU模式测试成功！
2025-08-13 09:22:11 - __main__ - INFO - GPU模式耗时: 26.41秒
2025-08-13 09:22:11 - __main__ - INFO -   voting_soft: F1=0.9700, 准确率=0.9700
2025-08-13 09:22:11 - __main__ - INFO -   voting_hard: F1=0.9683, 准确率=0.9683
2025-08-13 09:22:11 - __main__ - INFO - 
🚀 GPU加速比: 0.69x
2025-08-13 09:22:11 - __main__ - WARNING - ❌ GPU模式反而更慢（可能GPU初始化开销）
2025-08-13 09:22:11 - __main__ - INFO - 
================================================================================
2025-08-13 09:22:11 - __main__ - INFO - 测试并行性能对比
2025-08-13 09:22:11 - __main__ - INFO - ================================================================================
2025-08-13 09:22:11 - __main__ - INFO - 
测试1: 单线程模式
2025-08-13 09:22:11 - model_ensemble - INFO - ============================================================
2025-08-13 09:22:11 - model_ensemble - INFO - 开始运行集成学习管道
2025-08-13 09:22:11 - model_ensemble - INFO - ============================================================
2025-08-13 09:22:11 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-08-13 09:22:11 - model_ensemble - INFO - 集成方法: ['voting', 'bagging']
2025-08-13 09:22:11 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-08-13 09:22:11 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-08-13 09:22:12 - model_training - INFO - 模型名称: Random Forest
2025-08-13 09:22:12 - model_training - INFO - 准确率: 0.9467
2025-08-13 09:22:12 - model_training - INFO - AUC: 0.9826
2025-08-13 09:22:12 - model_training - INFO - AUPRC: 0.9658
2025-08-13 09:22:12 - model_training - INFO - 混淆矩阵:
2025-08-13 09:22:12 - model_training - INFO - 
[[141   9]
 [  7 143]]
2025-08-13 09:22:12 - model_training - INFO - 
分类报告:
2025-08-13 09:22:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.94      0.95       150
           1       0.94      0.95      0.95       150

    accuracy                           0.95       300
   macro avg       0.95      0.95      0.95       300
weighted avg       0.95      0.95      0.95       300

2025-08-13 09:22:12 - model_training - INFO - 训练时间: 0.19 秒
2025-08-13 09:22:12 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9467
2025-08-13 09:22:12 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-13 09:22:12 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-13 09:22:12 - model_ensemble - INFO -   RandomForest 训练完成
2025-08-13 09:22:12 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-08-13 09:22:12 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.006 以处理不平衡
2025-08-13 09:22:12 - model_training - INFO - 模型名称: XGBoost
2025-08-13 09:22:12 - model_training - INFO - 准确率: 0.9467
2025-08-13 09:22:12 - model_training - INFO - AUC: 0.9833
2025-08-13 09:22:12 - model_training - INFO - AUPRC: 0.9835
2025-08-13 09:22:12 - model_training - INFO - 混淆矩阵:
2025-08-13 09:22:12 - model_training - INFO - 
[[140  10]
 [  6 144]]
2025-08-13 09:22:12 - model_training - INFO - 
分类报告:
2025-08-13 09:22:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.96      0.93      0.95       150
           1       0.94      0.96      0.95       150

    accuracy                           0.95       300
   macro avg       0.95      0.95      0.95       300
weighted avg       0.95      0.95      0.95       300

2025-08-13 09:22:12 - model_training - INFO - 训练时间: 0.06 秒
2025-08-13 09:22:12 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9467
2025-08-13 09:22:12 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-13 09:22:12 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-13 09:22:12 - model_ensemble - INFO -   XGBoost 训练完成
2025-08-13 09:22:12 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-08-13 09:22:12 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-08-13 09:22:12 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-08-13 09:22:12 - model_ensemble - INFO -     正在优化 voting_soft 的权重...
2025-08-13 09:22:22 - model_ensemble - INFO - Voting权重优化完成: 最优CV f1_weighted=0.9242
2025-08-13 09:22:22 - model_ensemble - INFO -     使用优化权重: [0.87833958 0.12166042]
2025-08-13 09:22:22 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 09:22:22 - model_ensemble - INFO - 集成模型 voting_soft 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_soft_results.joblib
2025-08-13 09:22:22 - model_ensemble - INFO - 集成模型 voting_soft 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_soft_feature_names.joblib
2025-08-13 09:22:22 - model_ensemble - INFO -     voting_soft - 准确率: 0.9500, F1: 0.9500
2025-08-13 09:22:22 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-08-13 09:22:22 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-13 09:22:22 - model_ensemble - INFO - 集成模型 voting_hard 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_hard_results.joblib
2025-08-13 09:22:22 - model_ensemble - INFO - 集成模型 voting_hard 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Ensemble_voting_hard_feature_names.joblib
2025-08-13 09:22:22 - model_ensemble - INFO -     voting_hard - 准确率: 0.9467, F1: 0.9467
2025-08-13 09:22:22 - model_ensemble - INFO - 步骤2: 运行集成方法 - bagging
2025-08-13 09:22:22 - model_ensemble - INFO -     正在优化 bagging 的参数...
